# boston-q10-sync
Q10 + Clientify(q5) - <PERSON><PERSON> for syncing to SQL 2016

This code was done to sync data from Q10 to internal systems outside Q10.
It might be a bit of a monster due to API changes during the development but the last versions are working fine

# Codebase Overview: q5 and q10 Folders

This document provides an overview of the code found within the CLIENTIFY `q5` and Q10 `q10` sync folders of the project. It describes the purpose of each file and how they interact with each other.

## q5 Folder: Clientify Synchronization

The `q5` folder contains code responsible for synchronizing data with the "Clientify" service. This synchronization involves several entities: contacts, deals, tasks, users, and task types.

### Files and their Functionality:

*   **`contactos.js`**:
    *   **Purpose**: This file handles the synchronization of contacts between the local system and the Clientify service.
    *   **Functionality**:
        *   Fetches an authentication token using `get_token` from `primitives.js`.
        *   Uses `add_contacts` from `utils_contactos.js` to add or update contacts in Clientify.
        *   Logs progress and errors.
        *   Closes the connection using `close_connection` from `primitives.js`.
        * The `sync_contactos` is an async function that is used to sync the contacts.
        * The functions `getISODateTime` and `logError` are used for logging purposes.

*   **`deals.js`**:
    *   **Purpose**: This file (not provided in the code snippets, but implied by `sync_clientify.js`) likely handles the synchronization of deals (opportunities or sales) with the Clientify service.
    * **Functionality**:
        * Fetches an authentication token using `get_token` from `primitives.js`.
        * Uses some function to add or update the deals in Clientify.
        * Logs progress and errors.
        * Closes the connection using `close_connection` from `primitives.js`.

*   **`tasks_timeline.js`**:
    *   **Purpose**: This file (not provided in the code snippets, but implied by `sync_clientify.js`) likely handles the synchronization of tasks from the Clientify timeline.
    * **Functionality**:
        * Fetches an authentication token using `get_token` from `primitives.js`.
        * Uses some function to add or update the tasks from timeline in Clientify.
        * Logs progress and errors.
        * Closes the connection using `close_connection` from `primitives.js`.

*   **`tasks_types.js`**:
    *   **Purpose**: This file (not provided in the code snippets, but implied by `sync_clientify.js`) likely handles the synchronization of task types with Clientify.
    * **Functionality**:
        * Fetches an authentication token using `get_token` from `primitives.js`.
        * Uses some function to add or update the task types in Clientify.
        * Logs progress and errors.
        * Closes the connection using `close_connection` from `primitives.js`.

*   **`users.js`**:
    *   **Purpose**: This file (not provided in the code snippets, but implied by `sync_clientify.js`) likely handles the synchronization of users with Clientify.
    * **Functionality**:
        * Fetches an authentication token using `get_token` from `primitives.js`.
        * Uses some function to add or update the users in Clientify.
        * Logs progress and errors.
        * Closes the connection using `close_connection` from `primitives.js`.
*   **`sync_clientify.js`**:
    *   **Purpose**: This is the main entry point for synchronizing with Clientify. It orchestrates the synchronization of different entities.
    *   **Functionality**:
        *   Imports `usr` and `psw` from `config.js` for authentication.
        *   Imports `sync_contactos`, `sync_deals`, `sync_tasks_timeline`, `sync_task_types` and `sync_users`
        *   Uses `acquireLock` and `releaseLock` from `process_lock.js` to prevent concurrent synchronization processes.
        *   Calls the different `sync_` functions to synchronize task types, users, deals, contacts, and tasks.
        *   Handles errors and logs messages.
        *   Closes the connection using `close_connection` from `primitives.js`
        *   Uses the `getISODateTime` and `logError` functions for logging.

*   **`process_lock.js`**:
    * **Purpose**: This file (not provided in the code snippets, but implied by `sync_clientify.js`) likely implements a locking mechanism to prevent multiple synchronization processes from running simultaneously.
    * **Functionality**:
        *   `acquireLock`: Tries to obtain a lock. If successful returns true else false.
        *   `releaseLock`: Releases the lock.

* **`primitives.js`**:
    * **Purpose**: This file (not provided in the code snippets, but implied by `sync_clientify.js`) contains the functions that manage the connection and the token.
    * **Functionality**:
        * `to_cr_time_string`: Convert a Date to the correct format.
        * `get_token`: Request a token using usr and psw for the api.
        * `close_connection`: close the connection and delete the token.

* **`config.js`**:
    * **Purpose**: This file (not provided in the code snippets, but implied by `sync_clientify.js`) has the credentials for the api.
    * **Functionality**:
        * `usr`: Username to use in the api
        * `psw`: Password to use in the api

* **`utils_contactos.js`**:
    * **Purpose**: This file (not provided in the code snippets, but implied by `contactos.js`) has the function that manipulates the contacts in the api.
    * **Functionality**:
        * `add_page`: add a page to a contact
        * `get_contact`: get a contact.
        * `add_contacts`: add or update contacts.
        * `list_contacts`: list all contacts.

## q10 Folder: Q10 Synchronization

The `q10` folder is dedicated to synchronizing data with the "Q10" system. This includes entities like credits, courses, discounts, students, taxes, and more, related to an educational environment.

### Files and their Functionality:

*   **`creditos.js`**:
    *   **Purpose**: Handles synchronization of student credits.

*   **`cursos.js`**:
    *   **Purpose**: Handles synchronization of courses.

*   **`descuentos.js`**:
    *   **Purpose**: Handles synchronization of discounts.

*   **`estudiantes.js`**:
    *   **Purpose**: Handles synchronization of students.

*   **`impuestos.js`**:
    *   **Purpose**: Handles synchronization of taxes.

*   **`inscripcion.js`**:
    *   **Purpose**: Handles synchronization of enrollments.

*   **`ordenes_de_pago.js`**:
    *   **Purpose**: Handles synchronization of payment orders.

*   **`pagos_ordenes_de_pago.js`**:
    *   **Purpose**: Handles synchronization of payments associated with payment orders.

*   **`pagos.js`**:
    *   **Purpose**: Handles synchronization of payments.

*   **`periodos.js`**:
    *   **Purpose**: Handles synchronization of periods (semesters, terms, etc.).

*   **`programas.js`**:
    *   **Purpose**: Handles synchronization of academic programs.

*   **`sede_jornada.js`**:
    *   **Purpose**: Handles synchronization of location and session data.

*   **`preinscripciones.js`**:
    * **Purpose**: Handles synchronization of pre-enrollments.
*   **`grupos.js`**:
    * **Purpose**: Handles synchronization of the groups.
*   **`condiciones_matricula.js`**:
    *   **Purpose**: This file manages the synchronization of enrollment conditions from Q10 to the local database.
    *   **Functionality**:
        *   Uses `axios` to fetch data from the `CONDICION_MATRICULA_API_URL`.
        *   Uses `knex` to interact with a SQL database.
        *   `sync_condicion_matricula`: Inserts or updates enrollment condition records in the `Condicion_matricula` table.
        *   `get_condicion_matriculas`: Fetches enrollment conditions from the Q10 API based on the provided status.
        *   `get_and_sync_condicion_matriculas`: Fetches and then sync the data.
        * `sync_condicion_matriculas`: sync the condition for true and false.

*   **`pagos_de_pagos_pendientes.js`**:
    * **Purpose**: Handles synchronization of pending payments.
*   **`evaluaciones.js`**:
    *   **Purpose**: This file is responsible for synchronizing evaluations (grades, assessments) from Q10 with the local database.
    *   **Functionality**:
        *   Fetches data from the `EVALUACIONES_API_URL`, `CURSOS_API_URL` and `PROGRAMAS_API_URL`.
        *   Uses `knex` for database operations.
        *   `sync_evaluacion`: Inserts or updates evaluation records in the `Evaluacion` table.
        *   `get_curso_details`: Fetches details of a course from the Q10 API.
        *   `list_programas`: Fetches a list of programs from the Q10 API.
        *   `get_and_sync_evaluaciones`: Fetches and then syncs evaluations for a given program.
        * `get_evaluaciones`: Gets a list of evaluations.
        * `sync_evaluaciones`: sync the evaluations

*   **`inactivo_eval.js`**:
    * **Purpose**: This file has the same functionality as the `evaluaciones.js` file. It is probably a file that is not in use.
    * **Functionality**:
        * It has the same functionality that the `evaluaciones.js`

*   **`async_preinscripciones.js`**:
    *   **Purpose**: This file manages the synchronization of pre-enrollment data from Q10.
    *   **Functionality**:
        *   Uses `axios` to communicate with the Q10 API.
        *   Uses `knex` to interact with the SQL database.
        *   `sync_preinscripciones`: Manages the sync process of pre-enrollments.
        *   `list_sede_jornadas`: Fetches a list of locations and sessions.
        *   `list_programas`: Fetches a list of academic programs.
        *   `list_periodos`: Fetches a list of periods.
        * `list_cursos`: fetch the list of courses.
        * `get_preinscripciones`: get the pre-enrollments.
        * `async_get_preinscripciones`: get the pre-enrollments in async mode.

* **`cargas_academicas.js`**:
    * **Purpose**: This file (partially provided) is responsible for managing the synchronization of academic loads (courses/subjects a student is enrolled in).
    * **Functionality**:
        *  `sync_carga`: inserts or update the academic load.
        *  `list_periodos`: list the periods.

* **`estudiantes_comunidad_excel.js`**:
    * **Purpose**: This file (not provided) is responsible for managing the synchronization of students with a excel file.
    * **Functionality**:
        * Uses `exceljs` to read and manipulate an excel file.
        * Uses `knex` for database operations.
        * Sync the excel file data with the local database.

*   **`sync_q10.js`**:
    *   **Purpose**: This file is the primary orchestrator for the Q10 synchronization process.
    *   **Functionality**:
        *   Imports all the `sync_` functions from the other files.
        *   Uses `acquireLock` and `releaseLock` from `process_lock.js` to prevent concurrent synchronization processes.
        *   Calls the different `sync_` functions in a specific order.
        *   Logs progress, errors, and handles process termination signals.
        * Uses the `getISODateTime`, `log` and `logError` functions for logging.
*   **`process_lock.js`**:
    * **Purpose**: This file (not provided in the code snippets, but implied by `sync_q10.js`) likely implements a locking mechanism to prevent multiple synchronization processes from running simultaneously.
    * **Functionality**:
        *   `acquireLock`: Tries to obtain a lock. If successful returns true else false.
        *   `releaseLock`: Releases the lock.

* **`primitives.js`**:
    * **Purpose**: This file (partially provided) contains the functions used to format dates.
    * **Functionality**:
        * `to_cr_date_string`: Format date to the correct format.
        * `to_cr_datetime_string`: format datetime to the correct format.

* **`config.js`**:
    * **Purpose**: This file (partially provided) stores the credentials for the Q10 api.
    * **Functionality**:
        * `config`: connection to the database.
        * `q10_api_key`: api key to access the q10 api.
### General process
    * Get the data from the q10 api.
    * Verify if the data is already in the database.
    * if not insert the data, if it is update the data.

## Debugging

Both Sync use a similar approach: interacting with an external API using `axios`, managing the database using `knex`, and handling concurrency with `process_lock.js`. They also include a main orchestrator file (`sync_clientify.js` and `sync_q10.js`) to manage the entire synchronization workflow.

Due to this providers API limitation (Slow, fail prone, data migrated with inconsistent data that forces a full sync everytime) the API had to account for slow responses (waiting too long) and will keep running for more than 24hs when the cron run starts so there is a lock mechanism to avoid that.

### Commands to debug
Q10, Clientify and the CDR recors, all are run by cron. The cron calls a script that syncs ALL of the modues listed above. 
You can manually launche modules, for example if evaluaciones is failing, call it `node evaluaciones.js` and check its output

check The logs for issues `/home/<USER>/sync/q10.log` or `/home/<USER>/sync/q5/sync_clientify.log` both sync have a pretty verbose logging.

If you want to kill the process that runs the sync
 - `ps afux | grep sync` and then kill those processes `kill 83231 2312321 23032`.
 - After that delete the locks (if they exist, they should be cleanly removed by the process) `rm /home/<USER>/sync/q5/sync.lock` for clientify or `rm /home/<USER>/sync/q10/sync.lock`
  
If you need to check what are the crons (scheduled tasks) and call them manually `crontab -l`
Also, remember that SSH sessions to the terminal are usually unstable to keep open, so if you run a command it might be terminated (cancelling the process you started), use screen if needed.



# Updating the DB from Clientify

To update everything:

```
node sync_clientify.js
```

To update contacts:

```
node contactos.js
```

To update deals:

```
node deals.js
```

To update tasks, through contacts:

```
node tasks_timeline.js
```

To update task types:

```
node tasks_types.js
``

To update users:

```
node users.js
```

