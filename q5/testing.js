import axios from "axios";
import { config, usr, psw } from "./config.js";
import {
  to_cr_time_string,
  get_token,
  close_connection,
} from "./primitives.js";
import {
  add_page,
  add_contacto,
  list_pageof_contacts,
  fix_contacto,
  get_contact,
  get_page,
  add_contactos,
} from "./utils_contactos.js";
import {
  add_deal,
  add_deals,
  fix_deal,
  get_deal,
  list_one_deals_page,
  add_deals_page,
} from "./utils_deals.js";
import { fix_wallentry } from "./utils_wallentries.js";
import { add_test_wallentry } from "./test_wallentries.js";
import { add_test_task } from "./test_tasks.js";
import {
  fix_task,
  add_task,
  list_pageof_task_types,
  add_task_type,
} from "./utils_tasks.js";

export async function get_task_timeline(token, contact_id) {
  const full_contact = await get_contact(token, contact_id);

  console.log(full_contact.related_tasks);

  for (const i in full_contact.related_tasks) {
    const task = full_contact.related_tasks[i];
    let clean_task = undefined;
    try {
      clean_task = fix_task(task);
    } catch (error) {
      console.log(
        `Could not fix task from ${contact_id}: ${JSON.stringify(task)}`
      );
      throw error;
    }

    try {
      await add_task(clean_task, contact_id);
    } catch (error) {
      console.log(
        `Could not add task from ${contact_id}: ${JSON.stringify(task)}`
      );
      throw error;
    }
  }
}

const token = await get_token(usr, psw);

const pag_contactos = await get_page(token);
let clean_contactos = [];
for (const contacto of pag_contactos) {
  const clean_contacto = fix_contacto(contacto);
  clean_contactos.push(clean_contacto);
}

await add_contactos(clean_contactos);

// for (const contacto of clean_contactos) {
//   console.log(contacto);
//   console.log("---------");
// }

// const contact_id = 38174832;
// let contacto = await get_contact(token, contact_id);
// let full_contact = fix_contacto(contacto);

// console.log(full_contact.Cedula);

// let futuros = [];
// let added_contacto = undefined;

// added_contacto = get_task_timeline(token, 251934955);
// futuros.push(added_contacto);

// let results = undefined;
// try {
//   // Block until all contacts are done.
//   results = await Promise.allSettled(futuros);
// } catch (error) {
//   console.log("At least 1 of the previous ~25 contacts failed to sync.");
//   throw error;
// }

// const failed_contacts = results.filter((p) => p.status === "rejected");

// for (const i in failed_contacts) {
//   const failed_contact = failed_contacts[i];

//   console.log(`FAILED CONTACT URL: ${failed_contact.reason.config.url}`);
// }

// const [contacts, next_page] = await list_pageof_contacts(token);

console.log("+++++++++");

process.exit();
