import axios from "axios";
import { config, q10_api_key } from "./config.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});
import { get_evaluaciones } from "./evaluaciones.js";

// API URL
const ESTUDIANTES_API_URL = "https://api.q10.com/v1/estudiantes/";
const LIMIT = 800;
let OFFSET = 0;
let moreData = true;

// Call the function to start fetching and storing data
const evaluaciones = await get_evaluaciones(1106, "TI01-CLSA");
console.log(evaluaciones);
