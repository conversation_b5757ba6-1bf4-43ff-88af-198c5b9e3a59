import axios from "axios";
import knex from "knex";
import { config, usr, psw } from "./config.js";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});
import { to_cr_time_string, to_date_string } from "./primitives.js";
import { get_contact } from "./utils_contactos.js";

export async function clean_add_task(task, contact_id) {
  let clean_task = undefined;
  try {
    clean_task = fix_task(task);
  } catch (error) {
    throw error;
  }
  return await add_task(clean_task, contact_id);
}

export function fix_task(task) {
  let fixed_task = task;

  if (task.task_type != null) {
    try {
      fixed_task.task_type = Number(fixed_task.task_type.split("/").at(-2));
    } catch (error) {
      try {
        fixed_task.task_type = Number(fixed_task.task_type);
      } catch (error2) {
        console.log(
          `Could not get the task type for task: ${JSON.stringify(task)}`
        );
        throw error;
      }
    }
  }

  if (task.created != null) {
    fixed_task.created = to_cr_time_string(task.created);
  }
  if (task.modified != null) {
    fixed_task.modified = to_cr_time_string(task.modified);
  }
  if (task.start_datetime != null) {
    fixed_task.start_datetime = to_cr_time_string(task.start_datetime);
  }
  if (task.due_date != null) {
    fixed_task.due_date = to_cr_time_string(task.due_date);
  }
  if (task.end_datetime != null) {
    fixed_task.end_datetime = to_date_string(task.end_datetime);
  }

  return fixed_task;
}

export async function add_task(task, contacto_id) {
  await knex_conn("Actividades")
    .select()
    .where("id", task.id)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Actividades").insert({
          id: task.id,
          Contacto_id: contacto_id,
          Propietario: task.owner_name,
          Propietario_id: task.owner_id,
          Propietario_email: task.owner,
          Asignatario: task.assigned_to_name,
          Asignatario_id: task.assigned_to_id,
          Asignatario_email: task.assigned_to,
          Tipo: task.task_type,
          Estado: task.status,
          Estado_descripcion: task.status_desc,
          Creado: task.created,
          Modificado: task.modified,
          Inicio: task.start_datetime,
          Vencimiento: task.due_date,
          Fin: task.end_datetime,
        });
      } else {
        await knex_conn("Actividades")
          .update({
            Contacto_id: contacto_id,
            Propietario: task.owner_name,
            Propietario_id: task.owner_id,
            Propietario_email: task.owner,
            Asignatario: task.assigned_to_name,
            Asignatario_id: task.assigned_to_id,
            Asignatario_email: task.assigned_to,
            Tipo: task.task_type,
            Estado: task.status,
            Estado_descripcion: task.status_desc,
            Creado: task.created,
            Modificado: task.modified,
            Inicio: task.start_datetime,
            Vencimiento: task.due_date,
            Fin: task.end_datetime,
          })
          .where("id", task.id);
      }
    });
}

export async function get_task(api_key, task_id) {
  const TASKS_API_URL = `https://api.clientify.net/v1/tasks/${task_id}/`;
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  try {
    response = await axios.get(TASKS_API_URL, { headers: header });
  } catch (error) {
    console.log(error);
    throw error;
  }

  if (response.status == 200) {
    return response.data;
  } else {
    console.log(response);
    return "";
  }
}

export async function list_tasks(api_key) {
  const TASKS_API_URL = `https://api.clientify.net/v1/tasks/`;
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  try {
    response = await axios.get(TASKS_API_URL, { headers: header });
  } catch (error) {
    console.log(error);
    throw error;
  }

  if (response?.status == 200) {
    return response.data.results;
  } else {
    console.log(response);
    return "";
  }
}

export async function list_pageof_task_types(
  api_key,
  TASKS_API_URL = `https://api.clientify.net/v1/tasks/types?page_size=200`
) {
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  try {
    response = await axios.get(TASKS_API_URL, { headers: header });
  } catch (error) {
    console.log(error);
    throw error;
  }

  if (response?.status == 200) {
    return [response.data.results, response.data.next];
  } else {
    console.log(response);
    return "";
  }
}

export async function add_task_type(task_type) {
  try {
    const rows = await knex_conn("Actividades_tipos")
      .select("id") // Only select 'id' if that's all you need for the check
      .where("id", task_type.id);

    if (rows.length === 0) {
      await knex_conn("Actividades_tipos").insert({
        id: task_type.id,
        Descripcion: task_type.name,
      });
    } else {
      await knex_conn("Actividades_tipos")
        .update({
          Descripcion: task_type.name,
        })
        .where("id", task_type.id);
    }
  } catch (error) {
    console.error("Error in add_task_type:", error); // It's good to log the error here
    throw error; // Re-throw the error to be handled by the calling function
  }
}
