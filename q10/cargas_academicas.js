import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_date_string } from "./primitives.js";
import knex from "knex";

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const ESTUDIANTES_API_URL = "https://api.q10.com/v1/estudiantes";
const PERIODOS_API_URL = "https://api.q10.com/v1/periodos";
const CARGAS_ACADEMICAS_API_URL = "https://api.q10.com/v1/cargasacademicas";

const LIMIT = 800;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

async function sync_carga(estudiante, carga_academica) {
  log(
    `${estudiante.Codigo_estudiante} -- ${carga_academica.Codigo_matricula}`
  );

  await knex_conn("Carga_academica")
    .select()
    .where("Codigo_estudiante", estudiante.Codigo_estudiante)
    .where("Codigo_matricula", carga_academica.Codigo_matricula)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Carga_academica").insert({
          Codigo_estudiante: estudiante.Codigo_estudiante,
          Codigo_matricula: carga_academica.Codigo_matricula,
          Codigo_Programa: carga_academica.Codigo_Programa,
          Nombre_programa: carga_academica.Nombre_programa,
          Consecutivo_pensum: carga_academica.Consecutivo_pensum,
          Nombre_pensum: carga_academica.Nombre_pensum,
          Consecutivo_periodo: carga_academica.Consecutivo_periodo,
          Nombre_periodo: carga_academica.Nombre_periodo,
          Codigo_asignatura: carga_academica.Codigo_asignatura,
          Nombre_asignatura: carga_academica.Nombre_asignatura,
          Codigo_nivel: carga_academica.Codigo_nivel,
          Nombre_nivel: carga_academica.Nombre_nivel,
          Consecutivo_curso: carga_academica.Consecutivo_curso,
          Codigo_curso: carga_academica.Codigo_curso,
          Nombre_curso: carga_academica.Nombre_curso,
          Estado_matricula_asignatura:
            carga_academica.Estado_matricula_asignatura,
          Fecha_matricula_curso: carga_academica.Fecha_matricula_curso,
          Fecha_habilitacion: carga_academica.Fecha_habilitacion,
          Fecha_homologacion: carga_academica.Fecha_homologacion,
          Promedio_evaluacion: carga_academica.Promedio_evaluacion,
          Porcentaje_evaluado: carga_academica.Porcentaje_evaluado,
          Porcentaje_inasistencia: carga_academica.Porcentaje_inasistencia,
          Cantidad_inasistencia: carga_academica.Cantidad_inasistencia,
          Horario_semana: carga_academica.Horario_semana,
        });
      } else {
        await knex_conn("Carga_academica")
          .update({
            Codigo_Programa: carga_academica.Codigo_Programa,
            Nombre_programa: carga_academica.Nombre_programa,
            Consecutivo_pensum: carga_academica.Consecutivo_pensum,
            Nombre_pensum: carga_academica.Nombre_pensum,
            Consecutivo_periodo: carga_academica.Consecutivo_periodo,
            Nombre_periodo: carga_academica.Nombre_periodo,
            Codigo_asignatura: carga_academica.Codigo_asignatura,
            Nombre_asignatura: carga_academica.Nombre_asignatura,
            Codigo_nivel: carga_academica.Codigo_nivel,
            Nombre_nivel: carga_academica.Nombre_nivel,
            Consecutivo_curso: carga_academica.Consecutivo_curso,
            Codigo_curso: carga_academica.Codigo_curso,
            Nombre_curso: carga_academica.Nombre_curso,
            Estado_matricula_asignatura:
              carga_academica.Estado_matricula_asignatura,
            Fecha_matricula_curso: carga_academica.Fecha_matricula_curso,
            Fecha_habilitacion: carga_academica.Fecha_habilitacion,
            Fecha_homologacion: carga_academica.Fecha_homologacion,
            Promedio_evaluacion: carga_academica.Promedio_evaluacion,
            Porcentaje_evaluado: carga_academica.Porcentaje_evaluado,
            Porcentaje_inasistencia: carga_academica.Porcentaje_inasistencia,
            Cantidad_inasistencia: carga_academica.Cantidad_inasistencia,
            Horario_semana: carga_academica.Horario_semana,
          })
          .where("Codigo_estudiante", estudiante.Codigo_estudiante)
          .where("Codigo_matricula", carga_academica.Codigo_matricula);
      }
    });
}

export async function list_periodos() {
  OFFSET = 1;
  let periodos = [];
  try {
    let response = undefined;
    while (true) {
      try {
        // Fetch all courses
        response = await axios.get(PERIODOS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        logError(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      periodos.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  }

  return periodos;
}

export async function get_cargas_academicas(codigo_estudiante) {
  let cargas = [];
  OFFSET = 1;
  const req_link =
    CARGAS_ACADEMICAS_API_URL +
    `?Identificacion_estudiante=${codigo_estudiante}`;
  log(req_link);
  const instance = axios.create({
    baseURL: req_link,
    timeout: 30000,
    headers: { "API-Key": q10_api_key },
  });

  try {
    let response = undefined;
    while (true) {
      try {
        response = await instance.get(req_link, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
        });
      } catch (error) {
        logError(error.code);
        break;
      }
      if (response?.status != 200) {
        break;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      cargas.push(...structuredClone(response.data));

      // This API may say it has 0 items, so I'll be breaking after the first page:
      if (n_items == 0) {
        break;
      } else if (pages == actualPage) {
        break;
      } else {
        OFFSET += 1;
      }
    }
  } catch (error) {
    logError(error);
  }
  return cargas;
}

async function list_estudiantes(periodo) {
  OFFSET = 1;
  const search_url = ESTUDIANTES_API_URL + `?Periodo=${periodo.Consecutivo}`;

  let response = undefined;
  let estudiantes = [];
  try {
    while (true) {
      try {
        log(search_url);
        response = await axios.get(search_url, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        logError(
          `WARNING: error when getting students from periodo ${periodo.Consecutivo_periodo} at ${search_url}`
        );
        logError(error);
        break;
      }

      if (response?.status != 200) {
        logError(
          `WARNING: Could not get students from periodo ${periodo.Consecutivo_periodo}`
        );
        break;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      estudiantes.push(...structuredClone(response.data));
      OFFSET += 1;
      if (actualPage >= pages) {
        break;
      }
    }
  } catch (error) {
    logError(
      `WARNING: error when getting students from periodo ${periodo.Consecutivo_periodo}`
    );
    logError(error);
  }

  log(`Got ${estudiantes.length} estudiantes.`);

  return estudiantes;
}

export async function get_and_sync_cargas(periodos) {
  try {
    for (const i in periodos) {
      const periodo = periodos[i];
      log(`Getting estudiantes from periodo: ${periodo.Consecutivo} `);
      const estudiantes = await list_estudiantes(periodo);

      let futuros = [];
      for (const j in estudiantes) {
        const estudiante = estudiantes[j];
        const cargas_per_stdnt = await get_cargas_academicas(
          estudiante.Codigo_estudiante
        );

        log(cargas_per_stdnt);

        for (const k in cargas_per_stdnt) {
          futuros.push(sync_carga(estudiante, cargas_per_stdnt[k]));
        }
      }
      await Promise.allSettled(futuros).then((results) =>
        results.forEach((result) => {
          if (result.status === "fulfilled") {
            () => {};
          } else {
            logError(`FAILED carga_academica: ${result?.reason}`);
          }
        })
      );
      log("----");
    }
  } catch (error) {
    logError("Error:", error);
  }
}

export async function sync_cargas() {
  const periodos = await list_periodos();

  await get_and_sync_cargas(periodos);
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_cargas();
  process.exit();
}
