import axios from "axios";
import { config, usr, psw } from "./config.js";
import {
  to_cr_time_string,
  get_token,
  close_connection,
} from "./primitives.js";
import {
  add_page,
  get_contact,
  add_contacts,
  list_contacts,
} from "./utils_contactos.js";
import { add_deals, add_deals_page } from "./utils_deals.js";

export async function sync_deals(usr, psw) {
  let token = await get_token(usr, psw);
  try {
    await add_deals(token);
  } catch (error) {
    console.log('add_deals throw an error');
  }
  

  console.log(" --- done syncing deals --- ");
  return token;
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  const token = await sync_deals(usr, psw);
  await close_connection(token);
  process.exit();
}
