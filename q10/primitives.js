export function to_cr_datetime_string(datetime) {
  return new Date(
    new Date(datetime).toLocaleString("en-US", {
      timeZone: "America/Costa_Rica",
    })
  ).toISOString();
}

export function to_cr_date_string(datetime) {
  return new Date(
    new Date(datetime).toLocaleString("en-US", {
      timeZone: "America/Costa_Rica",
    })
  )
    .toISOString()
    .substring(0, 10);
}

export function to_datetime_string(in_date) {
  return new Date(in_date).toISOString();
}
