import axios from "axios";
import knex from "knex";
import { config, usr, psw } from "./config.js";
import { to_cr_time_string } from "./primitives.js";

const knex_conn = knex({
  client: "mssql",
  connection: config,
});
const CONTACTS_API_URL =
  "https://api.clientify.net/v1/contacts/:contact_id/addresses/";

export async function add_test_contacto(contacto) {
  await knex_conn("test_contactos")
    .select()
    .where("Contacto_id", contacto.id)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("test_contactos").insert({
          Contacto_id: contacto.id,
          Propietario: contacto.owner_name,
          Nombres: contacto.first_name,
          Apellidos: contacto.last_name,
          Cargo: contacto.title,
          Empresa: contacto.company,
          Estado: contacto.status,
          Emails: contacto.emails,
          Telefonos: contacto.phones,
          Origen: contacto.contact_source,
          Tipo: contacto.contact_type,
          Origen_web: contacto.medium,
          Custom_fields: contacto.custom_fields,
          Direcciones: contacto.addresses,
          Creado: contacto.created,
          Modificado: contacto.modified,
          Ultimo_contacto: contacto.last_contact,
        });
      } else {
        await knex_conn("test_contactos")
          .update({
            Propietario: contacto.owner_name,
            Nombres: contacto.first_name,
            Apellidos: contacto.last_name,
            Cargo: contacto.title,
            Empresa: contacto.company,
            Estado: contacto.status,
            Emails: contacto.emails,
            Telefonos: contacto.phones,
            Origen: contacto.contact_source,
            Tipo: contacto.contact_type,
            Origen_web: contacto.medium,
            Custom_fields: contacto.custom_fields,
            Direcciones: contacto.addresses,
            Creado: contacto.created,
            Modificado: contacto.modified,
            Ultimo_contacto: contacto.last_contact,
          })
          .where("Contacto_id", contacto.id);
      }
    });
}
