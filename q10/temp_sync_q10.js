import { sync_creditos } from "./creditos.js";
import { sync_cursos } from "./cursos.js";
import { sync_descuentos } from "./descuentos.js";
import { sync_estudiantes } from "./estudiantes.js";
import { sync_impuestos } from "./impuestos.js";

import { sync_inscripciones } from "./inscripcion.js";
import { sync_ordenes_de_pago } from "./ordenes_de_pago.js";
import { sync_pagos_ordenes_de_pago } from "./pagos_ordenes_de_pago.js";
import { sync_pagos } from "./pagos.js";
import { sync_periodos } from "./periodos.js";
import { sync_programas } from "./programas.js";
import { sync_sede_jornadas } from "./sede_jornada.js";
import { sync_preinscripciones } from "./preinscripciones.js";
import { sync_grupos } from "./grupos.js";
import { sync_condicion_matriculas } from "./condiciones_matricula.js";
import { sync_evaluaciones } from "./evaluaciones.js";

console.log(`--- starting --- ${new Date()}`);
try {
  await sync_sede_jornadas();
} catch (error) {
  console.log("sync_sede_jornadas failed: ");
  console.log(error);
}
console.log(`--- done syncing sede_jornadas --- ${new Date()}`);

// try {
//   await sync_creditos();
// } catch (error) {
//   console.log("sync_creditos failed: ");
//   console.log(error);
// }
// console.log(` --- done syncing creditos --- ${new Date()}`);

try {
  await sync_pagos_ordenes_de_pago();
} catch (error) {
  console.log("sync_pagos_ordenes_de_pago failed: ");
  console.log(error);
}
console.log(
  ` --- done syncing (pagos, formas, detalles) ordenes de pago --- ${new Date()}`
);

try {
  await sync_ordenes_de_pago();
} catch (error) {
  console.log("sync_ordenes_de_pago failed: ");
  console.log(error);
}
console.log(` --- done syncing ordenes de pago --- ${new Date()}`);

try {
  await sync_pagos();
} catch (error) {
  console.log("sync_pagos failed: ");
  console.log(error);
}
console.log(` --- done syncing pagos --- ${new Date()}`);

try {
  await sync_cursos();
} catch (error) {
  console.log("sync_cursos failed: ");
  console.log(error);
}
console.log(` --- done syncing cursos --- ${new Date()}`);

try {
  await sync_descuentos();
} catch (error) {
  console.log("sync_descuentos failed: ");
  console.log(error);
}
console.log(` --- done syncing descuentos --- ${new Date()}`);

try {
  await sync_estudiantes();
} catch (error) {
  console.log("sync_estudiantes failed: ");
  console.log(error);
}
console.log(` --- done syncing estudiantes --- ${new Date()}`);

try {
  await sync_impuestos();
} catch (error) {
  console.log("sync_impuestos failed: ");
  console.log(error);
}
console.log(` --- done syncing impuestos --- ${new Date()}`);

try {
  await sync_inscripciones();
} catch (error) {
  console.log("sync_inscripciones failed: ");
  console.log(error);
}
console.log(` --- done syncing inscripciones --- ${new Date()}`);

try {
  await sync_periodos();
} catch (error) {
  console.log("sync_periodos failed: ");
  console.log(error);
}
console.log(` --- done syncing periodos --- ${new Date()}`);

try {
  await sync_programas();
} catch (error) {
  console.log("sync_programas failed: ");
  console.log(error);
}
console.log(` --- done syncing programas --- ${new Date()}`);

try {
  await sync_preinscripciones();
} catch (error) {
  console.log("sync_preinscripciones failed: ");
  console.log(error);
}
console.log(` --- done syncing preinscripciones --- ${new Date()}`);

try {
  await sync_grupos();
} catch (error) {
  console.log("sync_grupos failed: ");
  console.log(error);
}
console.log(` --- done syncing sync_grupos --- ${new Date()}`);

try {
  await sync_condicion_matriculas();
} catch (error) {
  console.log("sync_condicion_matriculas failed: ");
  console.log(error);
}
console.log(` --- done syncing sync_condicion_matriculas --- ${new Date()}`);

try {
  await sync_evaluaciones();
} catch (error) {
  console.log("sync_evaluaciones failed: ");
  console.log(error);
}
console.log(` --- done syncing evaluaciones --- ${new Date()}`);

console.log(` --- done syncing everything --- ${new Date()}`);
process.exit();
