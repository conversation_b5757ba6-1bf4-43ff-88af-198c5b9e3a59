import { usr, psw } from "./config.js";
import { sync_contactos } from "./contactos.js";
import { sync_deals } from "./deals.js";
import { sync_tasks_timeline } from "./tasks_timeline.js";
import { sync_task_types } from "./tasks_types.js";
import { sync_users } from "./users.js";
import { acquireLock, releaseLock } from "./process_lock.js";
import { close_connection } from "./primitives.js";

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(message) {
  console.log(`${getISODateTime()} ${message}`);
}

function logError(message, error) {
  console.error(`${getISODateTime()} ${message}`, error);
}

async function runSync() {
  let token = null;
  try {
    if (!acquireLock()) {
      log("Exiting: Another sync process is already running");
      process.exit(1);
    }

    log(" --- start syncing task types --- ");
    await sync_task_types(usr, psw);
    log(" --- start syncing users --- ");
    await sync_users(usr, psw);
    log(" --- start syncing contactos  --- ");
    await sync_contactos(usr, psw);		
    log(" --- start syncing deals  --- ");
    await sync_deals(usr, psw);
    log(" --- start syncing tasks from timeline  --- ");
    token = await sync_tasks_timeline(usr, psw);
    log(" --- done syncing everything --- ");
  } catch (error) {
    logError("Sync process failed:", error);
    process.exit(1);
  } finally {
    if (token) {
      await close_connection(token);
    }
    releaseLock();
  }
  process.exit(0);
}

// Handle process termination
process.on('SIGINT', () => {
  log('Received SIGINT. Cleaning up...');
  releaseLock();
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('Received SIGTERM. Cleaning up...');
  releaseLock();
  process.exit(0);
});

// Start the sync process
runSync();
