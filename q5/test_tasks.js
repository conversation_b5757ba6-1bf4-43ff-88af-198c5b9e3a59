import axios from "axios";
import knex from "knex";
import { config, usr, psw } from "./config.js";
import { to_cr_time_string } from "./primitives.js";

const knex_conn = knex({
  client: "mssql",
  connection: config,
});

export async function add_test_task(task, contacto_id) {
  await knex_conn("test_Actividades")
    .select()
    .where("id", task.id)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("test_Actividades").insert({
          id: task.id,
          Contacto_id: contacto_id,
          Propietario: task.owner_name,
          Propietario_id: task.owner_id,
          Propietario_email: task.owner,
          Asignatario: task.assigned_to_name,
          Asignatario_id: task.assigned_to_id,
          Asignatario_email: task.assigned_to,
          Tipo: task.task_type,
          Estado: task.status,
          Estado_descripcion: task.status_desc,
          Creado: task.created,
          Modificado: task.modified,
          Inicio: task.start_datetime,
          Vencimiento: task.due_date,
          Fin: task.end_datetime,
        });
      } else {
        await knex_conn("test_Actividades")
          .update({
            Contacto_id: contacto_id,
            Propietario: task.owner_name,
            Propietario_id: task.owner_id,
            Propietario_email: task.owner,
            Asignatario: task.assigned_to_name,
            Asignatario_id: task.assigned_to_id,
            Asignatario_email: task.assigned_to,
            Tipo: task.task_type,
            Estado: task.status,
            Estado_descripcion: task.status_desc,
            Creado: task.created,
            Modificado: task.modified,
            Inicio: task.start_datetime,
            Vencimiento: task.due_date,
            Fin: task.end_datetime,
          })
          .where("id", task.id);
      }
    });
}
