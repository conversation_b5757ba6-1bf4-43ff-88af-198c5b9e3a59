import axios from "axios";
import { config, q10_api_key } from "./config.js";
import async from "async";
import knex from "knex";

const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const PAGOSCREDITOS_API_URL = "https://api.q10.com/v1/pagos/creditos";
const LIMIT = 400;
let OFFSET = 1;

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

export async function sync_pago(pago) {
  log(
    `Consecutivo_credito: ${pago.Consecutivo_credito} -- Numero_recibo_pago: ${pago.Numero_recibo_pago} -- Codigo_persona : ${pago.Codigo_persona}`
  );

  let nuevo_pago = false;
  await knex_conn("Pago")
    .select()
    .where("Consecutivo_credito", pago.Consecutivo_credito)
    .where("Numero_recibo_pago", pago.Numero_recibo_pago)
    .where("Codigo_persona", pago.Codigo_persona)
    .then(async (rows) => {
      if (rows.length === 0) {
        nuevo_pago = true;
        await knex_conn("Pago").insert({
          Consecutivo_credito: pago.Consecutivo_credito,
          Numero_recibo_pago: pago.Numero_recibo_pago,
          Codigo_persona: pago.Codigo_persona,
          Nombre_completo: pago.Nombre_completo,
          Numero_identificacion: pago.Numero_identificacion,
          Pagare: pago.Pagare,
          Prefijo_resolucion: pago.Prefijo_resolucion,
          Fecha_pago: pago.Fecha_pago,
          Fecha_Asiento: pago.Fecha_Asiento,
          Observacion: pago.Observacion,
          Saldo_capital_anterior: pago.Saldo_capital_anterior,
          Abono_interes_corriente: pago.Abono_interes_corriente,
          Abono_interes_mora: pago.Abono_interes_mora,
          Abono_penalizacion: pago.Abono_penalizacion,
          Abono_capital: pago.Abono_capital,
          Nuevo_saldo_capital: pago.Nuevo_saldo_capital,
          Codigo_cajero: pago.Codigo_cajero,
          Numero_identificacion_cajero: pago.Numero_identificacion_cajero,
          Nombre_cajero: pago.Nombre_cajero,
          Estado: pago.Estado,
        });
      } else {
        await knex_conn("Pago")
          .update({
            Codigo_persona: pago.Codigo_persona,
            Nombre_completo: pago.Nombre_completo,
            Numero_identificacion: pago.Numero_identificacion,
            Pagare: pago.Pagare,
            Prefijo_resolucion: pago.Prefijo_resolucion,
            Fecha_pago: pago.Fecha_pago,
            Fecha_Asiento: pago.Fecha_Asiento,
            Observacion: pago.Observacion,
            Saldo_capital_anterior: pago.Saldo_capital_anterior,
            Abono_interes_corriente: pago.Abono_interes_corriente,
            Abono_interes_mora: pago.Abono_interes_mora,
            Abono_penalizacion: pago.Abono_penalizacion,
            Abono_capital: pago.Abono_capital,
            Nuevo_saldo_capital: pago.Nuevo_saldo_capital,
            Codigo_cajero: pago.Codigo_cajero,
            Numero_identificacion_cajero: pago.Numero_identificacion_cajero,
            Nombre_cajero: pago.Nombre_cajero,
            Estado: pago.Estado,
          })
          .where("Consecutivo_credito", pago.Consecutivo_credito)
          .where("Numero_recibo_pago", pago.Numero_recibo_pago)
          .where("Codigo_persona", pago.Codigo_persona);
      }
    });
  return nuevo_pago;
}

export async function sync_Pago_formaPago_detalleCuota(pagoData) {
  try {
    await knex_conn.transaction(async (trx) => {
      const nuevo_pago = await sync_pago(pagoData);

      const formasPago = pagoData.Formas_pago;
      delete pagoData.Formas_pago;
      const detalleCuotas = pagoData.Detalle_cuotas;
      delete pagoData.Detalle_cuotas;
      if (nuevo_pago) {
        log(
          `Pago #${pagoData.Numero_recibo_pago} es nuevo, agregando a Forma_pago y Detalle_cuota .`
        );
        await Promise.all(
          formasPago.map((formaPago) =>
            trx
              .insert({
                Numero_recibo_pago: pagoData.Numero_recibo_pago,
                ...formaPago,
                Consecutivo_credito: pagoData.Consecutivo_credito,
                Codigo_estudiante: pagoData.Codigo_persona,
              })
              .into("Forma_pago")
          )
        );
        await Promise.all(
          detalleCuotas.map((detalleCuota) =>
            trx
              .insert({
                Numero_recibo_pago: pagoData.Numero_recibo_pago,
                ...detalleCuota,
                Consecutivo_credito: pagoData.Consecutivo_credito,
                Codigo_estudiante: pagoData.Codigo_persona,
              })
              .into("Detalle_cuota")
          )
        );
      }
    });
  } catch (error) {
    log(error);
  }
}

export async function sync_pago_formaPago_detalleCuota_pag(pagos) {
  let i = 0;
  for (const pagoData of pagos) {
    i += 1;
    log(`pago: ${i} of  ${pagos.length} - `);
    await sync_Pago_formaPago_detalleCuota(pagoData);
  }
}

export async function sync_pagos() {
  OFFSET = 1;
  const req_link = PAGOSCREDITOS_API_URL;

  const instance = axios.create({
    baseURL: req_link,
    timeout: 50000,
    headers: { "API-Key": q10_api_key },
  });

  let done = false;
  while (true) {
    try {
      let response = undefined;
      // Fetch data from the API for each period
      const newDate = new Date();
      newDate.setFullYear(newDate.getFullYear() - 1);
      const initialDateIso = newDate.toISOString().split("T")[0];
      const today = new Date();
      const endDateIso = today.toISOString().split("T")[0];
      while (true) {
        try {
          log(`${req_link} - ${initialDateIso} ${endDateIso}`);
          response = await instance.get(req_link, {
            params: {
              Limit: LIMIT,
              Offset: OFFSET,
              Fecha_inicio: initialDateIso,
              Fecha_fin: endDateIso,
            },
          });
        } catch (error) {
          log(error);
          break;
        }
        if (response?.status != 200) {
          break;
        }
        const pages = response.headers["x-paging-pagecount"];
        const actualPage = response.headers["x-paging-pagenumber"];
        const n_items = response.headers["x-paging-totalitemcount"];
        const n_items_page = response.headers["x-paging-pagesize"];

        log(
          `Total number of pages: ${pages}, items in total: ${n_items}, current page number: ${actualPage}, items on this page ${n_items_page}`
        );

        log(`Got ${response.data.length} pagos.`);
        await sync_pago_formaPago_detalleCuota_pag(response.data);

        if (pages == actualPage) {
          done = true;
          break;
        } else {
          OFFSET += 1;
        }
      }
    } catch (error) {
      log(error);
    }
    if (done) {
      break;
    }
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_pagos();
  process.exit();
}
