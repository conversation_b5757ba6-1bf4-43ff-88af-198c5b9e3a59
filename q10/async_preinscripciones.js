import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_date_string } from "./primitives.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const PROGRAMAS_API_URL = "https://api.q10.com/v1/programas";
const PERIODOS_API_URL = "https://api.q10.com/v1/periodos";
const SEDE_JORNADA_API_URL = "https://api.q10.com/v1/sedesjornadas";
const PREINSCRIPCIONES_API_URL = "https://api.q10.com/v1/preinscripciones";

const LIMIT = 800;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

async function sync_evaluacion(
  evaluacion,
  programa_codigo,
  periodo_consecutivo,
  sede_jornada_consecutivo
) {
  log(`${evaluacion.Codigo_estudiante} -- ${evaluacion.Codigo_curso}`);
  await knex_conn("Evaluacion")
    .select()
    .where("id_estudiante", evaluacion.Codigo_estudiante)
    .where("Codigo_curso", evaluacion.Codigo_curso)
    // .where("Programa_codigo", programa_codigo)
    // .where("Consecutivo_periodo", periodo_consecutivo)
    // .where("Consecutivo_sede_jornada", sede_jornada_consecutivo)
    .then(async (rows) => {
      if (rows.length === 0) {
        log(periodo_consecutivo);
        await knex_conn("Evaluacion").insert({
          id_estudiante: evaluacion.Codigo_estudiante,
          Codigo_curso: evaluacion.Codigo_curso,
          Programa_codigo: programa_codigo,
          Consecutivo_periodo: periodo_consecutivo,
          Consecutivo_sede_jornada: sede_jornada_consecutivo,
          Nombre_completo: evaluacion.Nombre_completo,
          Nombre_asignatura: evaluacion.Nombre_asignatura,
          Nombre_periodo: evaluacion.Nombre_periodo,
          Nombre_programa: evaluacion.Nombre_programa,
          Nombre_curso: evaluacion.Nombre_curso,
          Codigo_matricula: evaluacion.Codigo_matricula,
          Promedio_evaluacion: evaluacion.Promedio_evaluacion,
          Estado_matricula_asignatura: evaluacion.Estado_matricula_asignatura,
          Porcentaje_evaluado: evaluacion.Porcentaje_evaluado,
          Porcentaje_inasistencia: evaluacion.Porcentaje_inasistencia,
          Cantidad_inasistencia: evaluacion.Cantidad_inasistencia,
          Promedio_acumulado: evaluacion.Promedio_acumulado,
          Promedio_periodo: evaluacion.Promedio_periodo,
        });
      } else {
        log(programa_codigo);
        await knex_conn("Evaluacion")
          .update({
            Programa_codigo: programa_codigo,
            Consecutivo_periodo: periodo_consecutivo,
            Consecutivo_sede_jornada: sede_jornada_consecutivo,
            Nombre_completo: evaluacion.Nombre_completo,
            Nombre_asignatura: evaluacion.Nombre_asignatura,
            Nombre_periodo: evaluacion.Nombre_periodo,
            Nombre_programa: evaluacion.Nombre_programa,
            Nombre_curso: evaluacion.Nombre_curso,
            Codigo_matricula: evaluacion.Codigo_matricula,
            Promedio_evaluacion: evaluacion.Promedio_evaluacion,
            Estado_matricula_asignatura: evaluacion.Estado_matricula_asignatura,
            Porcentaje_evaluado: evaluacion.Porcentaje_evaluado,
            Porcentaje_inasistencia: evaluacion.Porcentaje_inasistencia,
            Cantidad_inasistencia: evaluacion.Cantidad_inasistencia,
            Promedio_acumulado: evaluacion.Promedio_acumulado,
            Promedio_periodo: evaluacion.Promedio_periodo,
          })
          .where("id_estudiante", evaluacion.Codigo_estudiante)
          .where("Codigo_curso", evaluacion.Codigo_curso);
        // .where("Programa_codigo", programa_codigo)
        // .where("Consecutivo_periodo", periodo_consecutivo)
        // .where("Consecutivo_sede_jornada", sede_jornada_consecutivo);
      }
    });
}

export async function sync_preinscripciones(cursos) {
  try {
    for (const i in cursos) {
      const curso = cursos[i];
      log(
        `${curso.Consecutivo}, ${curso.Codigo_programa}, ${curso.Consecutivo_periodo}`
      );

      let preinscripciones = undefined;
      try {
        preinscripciones = await get_preinscripciones(
          curso.Consecutivo,
          curso.Codigo_programa,
          curso.Consecutivo_periodo
        );
      } catch (error) {
        continue;
      }

      let futuros = [];
      for (const j in preinscripciones) {
        futuros.push(
          sync_evaluacion(
            preinscripciones[j],
            curso.Consecutivo,
            curso.Codigo_programa,
            curso.Consecutivo_periodo,
            curso.Consecutivo_sede_jornada
          )
        );
      }
      let results = undefined;
      try {
        results = await Promise.allSettled(futuros);
      } catch (error) {
        log(
          "At least 1 of the evaluations of this course failed to sync."
        );
      }
      // Record the ones that failed.
      const failed_preinscripciones = results.filter(
        (p) => p.status === "rejected"
      );
      for (const i in failed_preinscripciones) {
        const failed_evaluacion = failed_preinscripciones[i];
        log(`FAILED EVALUACION: ${failed_evaluacion?.reason}`);
      }
      log("-----------");
    }
  } catch (error) {
    logError("Error:", error);
  } finally {
    // Destroy the connection pool
    await knex_conn.destroy();
  }
}

export async function list_sede_jornadas() {
  let sede_jornadas = [];
  try {
    let response = undefined;

    while (true) {
      try {
        // Fetch all courses

        response = await axios.get(SEDE_JORNADA_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      sede_jornadas.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  } finally {
    // Destroy the connection pool
    await knex_conn.destroy();
  }

  return sede_jornadas;
}

export async function list_programas() {
  let programas = [];
  try {
    let response = undefined;

    while (true) {
      try {
        // Fetch all courses

        response = await axios.get(PROGRAMAS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      programas.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  } finally {
    // Destroy the connection pool
    await knex_conn.destroy();
  }

  return programas;
}

export async function list_periodos() {
  OFFSET = 1;
  let periodos = [];
  try {
    let response = undefined;
    while (true) {
      try {
        // Fetch all courses
        response = await axios.get(PERIODOS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      periodos.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  } finally {
    // Destroy the connection pool
    await knex_conn.destroy();
  }

  return periodos;
}

export async function list_cursos() {
  OFFSET = 1;
  let cursos = [];
  try {
    let response = undefined;
    while (true) {
      try {
        // Fetch all courses
        response = await axios.get(CURSOS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      cursos.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  } finally {
    // Destroy the connection pool
    await knex_conn.destroy();
  }

  return cursos;
}

export async function get_preinscripciones(
  sede_jornada,
  periodo,
  programa,
  wait
) {
  let preinscripciones = [];
  OFFSET = 1;
  const req_link =
    PREINSCRIPCIONES_API_URL +
    `?Sede_jornada=${sede_jornada}` +
    `&Periodo=${periodo}` +
    `&Programa=${programa}`;

  // if (wait == true) {
  //   setTimeout(function () {
  //     console.log(req_link);
  //   }, 500);
  // }
  log(req_link);
  const instance = axios.create({
    baseURL: req_link,
    timeout: 50000,
    headers: { "API-Key": q10_api_key },
  });

  try {
    let response = undefined;
    while (true) {
      try {
        response = await instance.get(req_link, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
        });
      } catch (error) {
        log(error);
        break;
      }
      if (response?.status != 200) {
        break;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      preinscripciones.push(...structuredClone(response.data));

      // This API always says it has 0 items, so I'll be breaking after the frist page:
      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }

      if (pages == actualPage) {
        break;
      } else {
        OFFSET += 1;
      }
    }
  } catch (error) {
    log(error);
  }
  return preinscripciones;
}

async function async_get_preinscripciones(sede_jornadas, periodos, programas) {
  let preinscripciones = [];
  for (const j in periodos) {
    for (const k in programas) {
      let futuros = [];
      for (const i in sede_jornadas) {
        futuros.push(
          get_preinscripciones(
            sede_jornadas[i].Consecutivo,
            periodos[j].Consecutivo,
            programas[k].Codigo,
            true
          )
        );
      }
      await Promise.allSettled(futuros).then((results) =>
        results.forEach((result) => {
          if (result.status === "fulfilled") {
            // console.log(result.status, result.value);
            preinscripciones.push(...structuredClone(result.value));
          } else {
            log(`FAILED PREINSCRIPCION: ${result?.reason}`);
          }
        })
      );
    }
    log(preinscripciones);
    log("------");
  }
  return preinscripciones;
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  log("Get programas");
  const programas = await list_programas();
  log("Get periodos");
  const periodos = await list_periodos();
  log("Get sede_jornadas");
  const sede_jornadas = await list_sede_jornadas();

  const preinscripciones = await async_get_preinscripciones(
    sede_jornadas,
    periodos,
    programas
  );

  log(preinscripciones);

  process.exit();
}
