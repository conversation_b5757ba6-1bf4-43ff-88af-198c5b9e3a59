// index.js
import axios from "axios";
import knex from 'knex';
import { config, cmw_usr, cmw_psw } from "../q5/config.js";


// Function to format date into the desired string format
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day} 00:00:00`;
}

// Calculate the start and end date
const today = new Date();
const oneDayAgo = new Date();
oneDayAgo.setDate(today.getDate() - 1);
today.setDate(today.getDate() + 1);

const dateStart = formatDate(oneDayAgo);
const dateEnd = formatDate(today);


//SQL Server configuration


const limit = 100;
let offset = 1;
config.database = 'CMW'
const db = knex({client: 'mssql', connection: config});
async function fetchAndInsertData() {
  try {
    while (true) {
        const options = {
            method: 'GET',
            url: 'https://callmyway.com/getCdrs.php',
            params: {
                username: cmw_usr,
                password: cmw_psw,
                dateStart: dateStart,
                dateEnd: dateEnd,
                ini: offset,
                cant: limit,
                format: 'json'
            }
        };

        const { data } = await axios.request(options);
        if (!data || !data.data.cdrs || data.data.cdrs.length === 0) {
            console.log('No more records or API returned an error, exiting.');
            break;
        }
        // console.log(data.data.cdrs)


        const cdrs = data.data.cdrs;
        const insertPromises = cdrs.map(cdr => {
            return db.raw(`
                INSERT INTO CDR (
                  callid,
                  original_callid,
                  ani,
                  ref_callid,
                  detect_time,
                  dnis,
                  disconnect_time,
                  connect_time,
                  ani_user,
                  duration,
                  cost,
                  dnis_user,
                  rate,
                  access_fee,
                  route_name,
                  route_pattern,
                  end_party,
                  type,
                  ani_ext,
                  end_reason,
                  detect_time_date,
                  dnis_ext,
                  ring_time,
                  detect_time_hour,
                  name_endpoint_ani,
                  name_endpoint_dnis
                )
                SELECT
                ?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                WHERE NOT EXISTS (SELECT 1 FROM CDR WHERE original_callid = ?)
              `, [
                  cdr.callid,
                  cdr.original_callid,
                  cdr.ani,
                  cdr.ref_callid,
                  cdr.detect_time,
                  cdr.dnis,
                  cdr.disconnect_time,
                  cdr.connect_time,
                  cdr.ani_user,
                  cdr.duration,
                  cdr.cost,
                  cdr.dnis_user,
                  cdr.rate,
                  cdr.access_fee,
                  cdr.route_name,
                  cdr.route_pattern,
                  cdr.end_party,
                  cdr.type,
                  cdr.ani_ext,
                  cdr.end_reason,
                  cdr.detect_time_date,
                  cdr.dnis_ext,
                  cdr.ring_time,
                  cdr.detect_time_hour,
                  cdr.name_endpoint_ani,
                  cdr.name_endpoint_dnis,
                   cdr.original_callid // Condition for where not exists
           ]);
        });

        await Promise.all(insertPromises);

        console.log(`Processed ${cdrs.length} records from offset: ${offset}`);
        offset += limit;

        if (cdrs.length < limit) {
            console.log('All records have been processed, exiting.');
            break;
        }
    }
  } catch (error) {
    console.error('Error during the fetching and inserting data process:', error);
  }
  finally{
      await db.destroy()
  }
}


fetchAndInsertData();