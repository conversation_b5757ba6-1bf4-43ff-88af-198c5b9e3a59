import axios from "axios";
import knex from "knex";
import { config, usr, psw } from "./config.js";
import { to_cr_time_string } from "./primitives.js";

const knex_conn = knex({
  client: "mssql",
  connection: config,
});

export async function add_test_wallentry(entry, contacto_id) {
  await knex_conn("test_Timeline")
    .select()
    .where("id", entry.id)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("test_Timeline").insert({
          id: entry.id,
          Contacto_id: contacto_id,
          Source_id: entry.source_id,
          Source_key: entry.source_key,
          Usuario: entry.user,
          Tipo: entry.type,
          Creado: entry.created,
          Fecha_extra: entry.extra_datetime,
          Extra: entry.extra,
        });
      } else {
        await knex_conn("test_Timeline")
          .update({
            Contacto_id: contacto_id,
            Source_id: entry.source_id,
            Source_key: entry.source_key,
            Usuario: entry.user,
            Tipo: entry.type,
            Creado: entry.created,
            Fecha_extra: entry.extra_datetime,
            Extra: entry.extra,
          })
          .where("id", entry.id);
      }
    });
}
