import axios from "axios";
import knex from "knex";
import { config, usr, psw } from "./config.js";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});
import { to_cr_time_string, to_date_string } from "./primitives.js";

export function fix_wallentry(entry) {
  let fixed_entry = entry;

  // Sometimes the source_id is not an int, but a token key (eg: type==website_session)
  fixed_entry.source_id = Number(entry.source_id);
  if (isNaN(fixed_entry.source_id)) {
    fixed_entry.source_id = null;
    fixed_entry.source_key = String(entry.source_id);
  } else {
    fixed_entry.source_key = null;
  }

  if (entry.created != null) {
    fixed_entry.created = to_cr_time_string(entry.created);
  }
  if (entry.extra_datetime != null) {
    fixed_entry.extra_datetime = to_cr_time_string(entry.extra_datetime);
  }

  return fixed_entry;
}

export async function get_entry(api_key, entry_id) {
  const entryS_API_URL = `https://api.clientify.net/v1/entrys/${entry_id}/`;
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  try {
    response = await axios.get(entryS_API_URL, { headers: header });
  } catch (error) {
    console.log(error);
    throw error;
  }

  if (response.status == 200) {
    return response.data;
  } else {
    console.log(response);
    return "";
  }
}

export async function list_wallentries(api_key) {
  const WALLENTRIES_API_URL = `https://api.clientify.net/v1/wall-entries?page_size=200`;
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  try {
    response = await axios.get(WALLENTRIES_API_URL, { headers: header });
  } catch (error) {
    console.log(error);
    throw error;
  }

  if (response?.status == 200) {
    return response.data;
  } else {
    console.log(response);
    return "";
  }
}

export async function add_wallentry(entry, contacto_id) {
  await knex_conn("Timeline")
    .select()
    .where("id", entry.id)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Timeline").insert({
          id: entry.id,
          Contacto_id: contacto_id,
          Source_id: entry.source_id,
          Source_key: entry.source_key,
          Usuario: entry.user,
          Tipo: entry.type,
          Creado: entry.created,
          Fecha_extra: entry.extra_datetime,
          Extra: entry.extra,
        });
      } else {
        await knex_conn("Timeline")
          .update({
            Contacto_id: contacto_id,
            Source_id: entry.source_id,
            Source_key: entry.source_key,
            Usuario: entry.user,
            Tipo: entry.type,
            Creado: entry.created,
            Fecha_extra: entry.extra_datetime,
            Extra: entry.extra,
          })
          .where("id", entry.id);
      }
    });
}
