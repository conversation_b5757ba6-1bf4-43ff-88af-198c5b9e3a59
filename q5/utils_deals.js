import axios from "axios";
import knex from "knex";
import { config, usr, psw } from "./config.js";
import { RateLimiterMemory } from "rate-limiter-flexible";
import { saveLastPage, getLastPage, isPageDataRecent } from "./page_tracker.js";

const rateLimiter = new RateLimiterMemory({
  points: 100, // 60 requests per minute
  duration: 60, // 1 minute
});

// Retry configuration
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

axios.interceptors.request.use(
  async (config) => {
    try {
      await rateLimiter.consume(config.baseURL || "global"); // Use baseURL or a global key
    } catch (error) {
      const timeToWait = Math.floor(error.msBeforeNext / 1000) || 1;
      await new Promise((resolve) => setTimeout(resolve, error.msBeforeNext));
    }
    return config; // If no rate limit is hit, proceed with the request
  },
  (error) => {
    if (axios.isAxiosError(error)) {
      // Type guard
      if (error.response) {
        console.error(
          "Server responded with an error:",
          error.response.status,
          error.response.data
        );
      } else if (error.request) {
        // console.error("No response received:", error.request);
      } else {
        console.error("Error setting up the request:", error.message);
      }
    } else {
      console.error("An unexpected error occurred:", error);
    }
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const config = error.config;

    // Add retry count to config if it doesn't exist
    if (!config) return Promise.reject(error);

    config.retryCount = config.retryCount ?? 0;

    const shouldRetry = (error) => {
      return (
        config.retryCount < MAX_RETRIES &&
        (error.code === "ECONNRESET" ||
          error.code === "ECONNABORTED" ||
          error.code === "ETIMEDOUT" ||
          error.response?.status === 429 || // Too Many Requests
          (error.response?.status ?? 0) >= 500) // Server errors
      );
    };

    if (shouldRetry(error)) {
      config.retryCount += 1;

      // Exponential backoff
      const backoffTime = RETRY_DELAY * Math.pow(2, config.retryCount - 1);

      console.warn(
        `Retry attempt ${config.retryCount} for ${config.url} after ${backoffTime}ms`,
        error.code ? `Error code: ${error.code}` : "",
        error.response?.status ? `Status: ${error.response.status}` : ""
      );

      await delay(backoffTime);
      return axios(config);
    }

    // If error shouldn't be retried or max retries reached
    if (axios.isAxiosError(error)) {
      let errorMessage = "Request failed";

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        errorMessage = `Server error: ${error.response.status} ${error.response.statusText}`;
        console.error("Error data:", error.response.data);
        console.error("Error status:", error.response.status);
        console.error("Error headers:", error.response.headers);
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = "No response received from server";
        if (error.code === "ECONNRESET") {
          errorMessage = "Connection reset by server";
        }
        console.error("Error request:", errorMessage);
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = error.message;
        console.error("Error message:", error.message);
      }

      const enhancedError = new Error(errorMessage);
      enhancedError.cause = error;
      return Promise.reject(enhancedError);
    }

    return Promise.reject(error);
  }
);

const knex_conn = knex({
  client: "mssql",
  connection: config,
});
import { to_cr_time_string, to_date_string } from "./primitives.js";

const clean_add_deal = async (deal, api_key = "") => {
  let clean_deal = undefined;
  try {
    clean_deal = await fix_deal(deal, api_key);
  } catch (error) {
    throw error;
  }
  return await add_deal(clean_deal, clean_deal.contact_id);
};

export async function add_deals(api_key) {
  const DEALS_API_URL = `https://api.clientify.net/v1/deals/`;
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let { pageNumber, timestamp } = getLastPage();
  if (!isPageDataRecent(timestamp)) {
    pageNumber = 0; // Start from the first page if data is not recent
  }

  let next_page = pageNumber
    ? `${DEALS_API_URL}?page_size=200&page=${pageNumber}`
    : DEALS_API_URL;
  console.log(`Requesting Deals URL ${pageNumber}`)
  await axios
    .get(next_page, { headers: header })
    .then(async function (response) {
      if (response.status != 200) {
        console.error(response);
      } else {
        while (next_page != null) {
          next_page = response.data.next;
          await add_deals_page(response.data.results, api_key);
          console.log(`${getISODateTime()} - Page done - `, next_page);
          saveLastPage(
            next_page ? new URL(next_page).searchParams.get("page") : null
          );
          if (next_page == null) {
            break;
          }
          response = await axios.get(next_page, { headers: header });
          if (response.status != 200) {
            console.log(response.text);
            break;
          }
        }
      }
      return;
    })
    .catch(function (error) {
      // console.log(error);
      throw error;
    });
}

export async function add_deals_page(deals_list, api_key = "") {
  let futuros = [];
  for (const deal of deals_list) {
    log(`Deal ID Processed: ${deal.id}`);
    let clean_deal = undefined;
    try {
      clean_deal = clean_add_deal(deal, api_key);
    } catch (error) {
      console.error(`Could not queue deal: ${JSON.stringify(deal)}`);
      continue;
    }
    futuros.push(clean_deal);
  }

  log(`-------- Page finished --------`);
  try {
    await Promise.all(futuros);
  } catch (error) {
    log("At least 1 of the previous ~25 deals ids failed to sync.");
    throw error;
  }
}

export async function fix_deal(deal, api_key = "") {
  let fixed_deal = deal;
  if ((deal.status = 4)) {
    //Its Lost
    const dealItem = await get_deal(api_key, deal.id);
    if (dealItem.lost_reason != null) {
      deal.RazonPerdida = dealItem.lost_reason;
    }
  }
  if (deal.created != null) {
    fixed_deal.created = to_cr_time_string(deal.created);
  }
  if (deal.modified != null) {
    fixed_deal.modified = to_cr_time_string(deal.modified);
  }
  if (deal.expected_closed_date != null) {
    fixed_deal.expected_closed_date = to_date_string(deal.expected_closed_date);
  }
  if (deal.actual_closed_date != null) {
    fixed_deal.actual_closed_date = to_date_string(deal.actual_closed_date);
  }
  if (deal.pipeline_stage_desc != null) {
    fixed_deal.Etapa = String(deal.pipeline_stage_desc);
  }
  if (deal.pipeline_desc != null) {
    fixed_deal.ProcesoVentas = String(deal.pipeline_desc);
  }

  if (deal.custom_fields != null && deal.custom_fields.length > 0) {
    for (const item in deal.custom_fields) {
      const entry = deal.custom_fields[item];

      if (entry.field === "Periodo académico") {
        fixed_deal.Periodo = String(entry.value);
      }
      if (entry.field === "Carrera de matrícula") {
        fixed_deal.Carrera = String(entry.value);
      }
      if (entry.field === "Cédula") {
        fixed_deal.Cedula = String(entry.value);
      }
    }
  }
  // Finally, retrieve the contact id
  try {
    fixed_deal.contact_id = Number(deal.contact.split("/").at(-2));
  } catch (error) {
    try {
      fixed_deal.contact_id = Number(deal.contact);
    } catch (error2) {
      console.log(
        `Could not get the contact id for deal: ${JSON.stringify(deal)}`
      );
      throw error;
    }
  }

  return fixed_deal;
}

export async function add_deal(deal, contacto_id) {
  if (deal.id != null) {
    await knex_conn("Deals")
      .select()
      .where("Deal_id", deal.id)
      .then(async (rows) => {
        try {
          if (rows.length === 0) {
            await knex_conn("Deals").insert({
              Deal_id: deal.id,
              Contacto_id: contacto_id,
              Deal_url: deal.url,
              Propietario: deal.owner_name,
              Propietario_mail: deal.owner,
              Nombre: deal.contact_name,
              Email: deal.contact_email,
              Telefono: deal.contact_phone,
              Origen: deal.contact_source,
              Medio: deal.contact_medium,
              Estado: deal.status,
              Estado_desc: deal.status_desc,
              Periodo: deal.Periodo ? deal.Periodo : "",
              Cedula: deal.Cedula ? deal.Cedula : "",
              Carrera: deal.Carrera ? deal.Carrera : "",
              ProcesoVentas: deal.ProcesoVentas ? deal.ProcesoVentas : "",
              Etapa: deal.Etapa ? deal.Etapa : "",
              RazonPerdida: deal.RazonPerdida ? deal.RazonPerdida : "",
              Creado: deal.created,
              Modificado: deal.modified,
              Expiracion_esperada: deal.expected_closed_date,
              Expiracion_final: deal.actual_closed_date,
            });
          } else {
            await knex_conn("Deals")
              .update({
                Contacto_id: contacto_id,
                Deal_url: deal.url,
                Propietario: deal.owner_name,
                Propietario_mail: deal.owner,
                Nombre: deal.contact_name,
                Email: deal.contact_email,
                Telefono: deal.contact_phone,
                Origen: deal.contact_source,
                Medio: deal.contact_medium,
                Estado: deal.status,
                Estado_desc: deal.status_desc,
                Periodo: deal.Periodo ? deal.Periodo : "",
                Cedula: deal.Cedula ? deal.Cedula : "",
                Carrera: deal.Carrera ? deal.Carrera : "",
                ProcesoVentas: deal.ProcesoVentas ? deal.ProcesoVentas : "",
                Etapa: deal.Etapa ? deal.Etapa : "",
                RazonPerdida: deal.RazonPerdida ? deal.RazonPerdida : "",
                Creado: deal.created,
                Modificado: deal.modified,
                Expiracion_esperada: deal.expected_closed_date,
                Expiracion_final: deal.actual_closed_date,
              })
              .where("Deal_id", deal.id);
          }
        } catch (error) {
          console.error(
            `Error when inserting/updating DB: ${contacto_id} deal id: ${deal.id}`
          );
          console.error(error);
        }
      });
  } else {
    console.log("DEAL", deal);
  }
  if ((deal.contact = null)) return deal.id;
}

export async function get_deal(api_key, deal_id) {
  const DEALS_API_URL = `https://api.clientify.net/v1/deals/${deal_id}/`;
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  try {
    response = await axios.get(DEALS_API_URL, { headers: header });
  } catch (error) {
    console.log(error);
    throw error;
  }

  if (response.status == 200) {
    return response.data;
  } else {
    console.error(response);
    return "";
  }
}

export async function list_one_deals_page(api_key) {
  const DEALS_API_URL = `https://api.clientify.net/v1/deals/`;
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  try {
    response = await axios.get(DEALS_API_URL, { headers: header });
  } catch (error) {
    console.log(error);
    throw error;
  }

  if (response.status == 200) {
    return response.data;
  } else {
    console.log(response);
    return "";
  }
}

function getISODateTime() {
  const now = new Date();
  return (
    now.toISOString().replace("T", " ").split(".")[0] +
    "." +
    now.getMilliseconds().toString().padStart(3, "0")
  );
}
function log(...args) {
  console.log(getISODateTime(), ...args);
}
