import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_date_string } from "./primitives.js";
import knex from "knex";

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const CURSOS_API_URL = "https://api.q10.com/v1/cursos/";
const LIMIT = 800;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

export async function sync_cursos() {
  try {
    let response = undefined;
    while (true) {
      try {
        const req_link =
          CURSOS_API_URL +
          `?Limit=${LIMIT}` +
          `&Offset=${OFFSET}` +
          `&Estado=Abierto`;
        log(req_link);
        response = await axios.get(req_link, {
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(`Estudiante: ${id.Codigo_estudiante} no existe`);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }

      for (const curso of response.data) {
        log(
          `${curso.Codigo} - ${to_cr_date_string(
            curso.Fecha_inicio
          )} - ${to_cr_date_string(curso.Fecha_fin)}`
        );

        await knex_conn("Curso")
          .select()
          .where("id", Number(curso.Consecutivo))
          .then(async (rows) => {
            if (rows.length === 0) {
              await knex_conn("Curso").insert({
                id: curso.Consecutivo,
                Codigo: curso.Codigo,
                Nombre: curso.Nombre,
                Nombre_docente: curso.Nombre_docente,
                Cupo_maximo: curso.Cupo_maximo,
                Periodo_id: curso.Consecutivo_periodo,
                Periodo_nombre: curso.Nombre_periodo,
                Sede_jornada_id: curso.Consecutivo_sede_jornada,
                Sede_jornada_nombre: curso.Nombre_sede_jornada,
                Programa_codigo: curso.Codigo_programa,
                Programa_nombre: curso.Nombre_programa,
                Estado: curso.Estado,
                Codigo_programa: curso.Codigo_programa,
                Consecutivo_pensum_programa: curso.Consecutivo_pensum_programa,
                Fecha_inicio: to_cr_date_string(curso.Fecha_inicio),
                Fecha_fin: to_cr_date_string(curso.Fecha_fin),
                Cantidad_estudiantes_matriculados:
                  curso.Cantidad_estudiantes_matriculados,
              });
            } else {
              await knex_conn("Curso")
                .update({
                  Codigo: curso.Codigo,
                  Nombre: curso.Nombre,
                  Nombre_docente: curso.Nombre_docente,
                  Cupo_maximo: curso.Cupo_maximo,
                  Periodo_id: curso.Consecutivo_periodo,
                  Periodo_nombre: curso.Nombre_periodo,
                  Sede_jornada_id: curso.Consecutivo_sede_jornada,
                  Sede_jornada_nombre: curso.Nombre_sede_jornada,
                  Programa_codigo: curso.Codigo_programa,
                  Programa_nombre: curso.Nombre_programa,
                  Estado: curso.Estado,
                  Codigo_programa: curso.Codigo_programa,
                  Consecutivo_pensum_programa:
                    curso.Consecutivo_pensum_programa,
                  Fecha_inicio: to_cr_date_string(curso.Fecha_inicio),
                  Fecha_fin: to_cr_date_string(curso.Fecha_fin),
                  Cantidad_estudiantes_matriculados:
                    curso.Cantidad_estudiantes_matriculados,
                })
                .where("id", curso.Consecutivo);
            }
          });
      }
    }
  } catch (error) {
    logError("Error:", error);
  } finally {
    // Destroy the connection pool
    await knex_conn.destroy();
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  // Call the function to start fetching and storing data
  await sync_cursos();
  process.exit();
}
