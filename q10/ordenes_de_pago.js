import axios from "axios";
import { config, q10_api_key } from "./config.js";
import knex from "knex";

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const ORDENES_DE_PAGO_API_URL = "https://api.q10.com/v1/ordenespago";
const LIMIT = 400;
let OFFSET = 0;
let moreData = true;

// These file syncs table Ordenes_pago with ordenes de pago from
// https://.../ordenespago. Some of these come from credits, so we need to run
// creditos.js to get that info.

// FIX!

export async function sync_orden_de_pago(ordenPago) {
  let nueva_ordenPago = false;
  await knex_conn("Ordenes_pago")
    .select()
    .where("Numero_orden_pago", ordenPago.Numero_orden_pago)
    .then(async (rows) => {
      if (rows.length === 0) {
        nueva_ordenPago = true;
        await knex_conn("Ordenes_pago").insert({
          Numero_orden_pago: ordenPago.Numero_orden_pago,
          Consecutivo_credito: ordenPago.Consecutivo_credito,
          Fecha_orden_pago: ordenPago.Fecha_orden_pago,
          Valor_total: ordenPago.Valor_total,
          Valor_en_este_credito: ordenPago.Valor_en_este_credito,
          Consecutivo_Periodo: ordenPago.Consecutivo_Periodo,
          Nombre_periodo: ordenPago.Nombre_periodo,
          Estado_orden_pago: ordenPago.Estado_orden_pago,
          Valor_pagado: ordenPago.Valor_pagado,
          Justificacion_anulacion: ordenPago.Justificacion_anulacion,
          Anulada: ordenPago.Anulada,
          Observaciones_orden_pago: ordenPago.Observaciones_orden_pago,
          Numero_identificacion_cajero: ordenPago.Numero_identificacion_cajero,
          Fecha_vencimiento_1: ordenPago.Fecha_vencimiento_1,
          Codigo_persona: ordenPago.Codigo_persona,
          Fecha_anulacion: ordenPago.Fecha_anulacion,
          Numero_identificacion: ordenPago.Numero_identificacion,
          Orden_pago_paz_y_salvo: ordenPago.Orden_pago_paz_y_salvo,
          Valor_pendiente: ordenPago.Valor_pendiente,
          Nombre_cajero: ordenPago.Nombre_cajero,
          Codigo_cajero: ordenPago.Codigo_cajero,
          Codigo_programa: ordenPago.Codigo_programa,
          Nombre_programa: ordenPago.Nombre_programa,
          Codigo_sede: ordenPago.Codigo_sede,
          Nombre_sede: ordenPago.Nombre_sede,
          Codigo_jornada: ordenPago.Codigo_jornada,
          Nombre_jornada: ordenPago.Nombre_jornada,
        });
      } else {
        await knex_conn("Ordenes_pago")
          .update({
            Consecutivo_credito: ordenPago.Consecutivo_credito,
            Fecha_orden_pago: ordenPago.Fecha_orden_pago,
            Valor_total: ordenPago.Valor_total,
            Valor_en_este_credito: ordenPago.Valor_en_este_credito,
            Consecutivo_Periodo: ordenPago.Consecutivo_Periodo,
            Nombre_periodo: ordenPago.Nombre_periodo,
            Estado_orden_pago: ordenPago.Estado_orden_pago,
            Valor_pagado: ordenPago.Valor_pagado,
            Justificacion_anulacion: ordenPago.Justificacion_anulacion,
            Anulada: ordenPago.Anulada,
            Observaciones_orden_pago: ordenPago.Observaciones_orden_pago,
            Numero_identificacion_cajero:
              ordenPago.Numero_identificacion_cajero,
            Fecha_vencimiento_1: ordenPago.Fecha_vencimiento_1,
            Codigo_persona: ordenPago.Codigo_persona,
            Fecha_anulacion: ordenPago.Fecha_anulacion,
            Numero_identificacion: ordenPago.Numero_identificacion,
            Orden_pago_paz_y_salvo: ordenPago.Orden_pago_paz_y_salvo,
            Valor_pendiente: ordenPago.Valor_pendiente,
            Nombre_cajero: ordenPago.Nombre_cajero,
            Codigo_cajero: ordenPago.Codigo_cajero,
            Codigo_programa: ordenPago.Codigo_programa,
            Nombre_programa: ordenPago.Nombre_programa,
            Codigo_sede: ordenPago.Codigo_sede,
            Nombre_sede: ordenPago.Nombre_sede,
            Codigo_jornada: ordenPago.Codigo_jornada,
            Nombre_jornada: ordenPago.Nombre_jornada,
          })
          .where("Numero_orden_pago", ordenPago.Numero_orden_pago);
      }
    });
  return nueva_ordenPago;
}

export async function sync_detalle(Numero_orden_pago, id_detalle, detalle) {
  await knex_conn("Detalle_Orden_Pago")
    .select()
    .where("Numero_orden_pago", Numero_orden_pago)
    .where("id_detalle", id_detalle)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Detalle_Orden_Pago").insert({
          Numero_orden_pago: Numero_orden_pago,
          id_detalle: id_detalle,
          Consecutivo_pago_pendiente: detalle.Consecutivo_pago_pendiente,
          Nombre_periodo: detalle.Nombre_periodo,
          Codigo_producto: detalle.Codigo_producto,
          Nombre_producto: detalle.Nombre_producto,
          Consecutivo_periodo: detalle.Consecutivo_periodo,
          Valor_producto: detalle.Valor_producto,
          Nombre_impuesto: detalle.Nombre_impuesto,
          Porcentaje_impuesto: detalle.Porcentaje_impuesto,
          Valor_impuesto: detalle.Valor_impuesto,
          Cantidad: detalle.Cantidad,
          Valor_total: detalle.Valor_total,
        });
      } else {
        await knex_conn("Detalle_Orden_Pago")
          .update({
            Consecutivo_pago_pendiente: detalle.Consecutivo_pago_pendiente,
            id_detalle: id_detalle,
            Nombre_periodo: detalle.Nombre_periodo,
            Codigo_producto: detalle.Codigo_producto,
            Nombre_producto: detalle.Nombre_producto,
            Consecutivo_periodo: detalle.Consecutivo_periodo,
            Valor_producto: detalle.Valor_producto,
            Nombre_impuesto: detalle.Nombre_impuesto,
            Porcentaje_impuesto: detalle.Porcentaje_impuesto,
            Valor_impuesto: detalle.Valor_impuesto,
            Cantidad: detalle.Cantidad,
            Valor_total: detalle.Valor_total,
          })
          .where("id_detalle", id_detalle)
          .where("Numero_orden_pago", Numero_orden_pago);
      }
    });
  return;
}

export async function sync_ordenes_de_pago() {
  // Fetch data from the API for each period
  let response = undefined;
  // Given date string "2023-01-01"
  // const initialDateIso = newDate.toISOString().split("T")[0];
  // const endDateIso = today.toISOString().split("T")[0];
  const newDate = new Date();
  newDate.setFullYear(newDate.getFullYear() - 1);
  const today = new Date();
  const initialDateIso = newDate.toISOString().split("T")[0];
  const endDateIso = today.toISOString().split("T")[0];
  moreData = true;
  while (moreData) {
    //Get that at least once, if more data is avail, do it again
    moreData = false;
    try {
      response = await axios.get(ORDENES_DE_PAGO_API_URL, {
        params: {
          Limit: LIMIT,
          Offset: OFFSET,
          Fecha_inicio: initialDateIso,
          Fecha_fin: endDateIso,
        },
        headers: {
          "API-Key": q10_api_key,
        },
      });
    } catch (error) {
      console.log("No hay pagos de creditos", error);
    }
    const pages = response?.headers["x-paging-pagecount"];
    const totalResults = response?.headers["x-paging-totalitemcount"];
    const actualPage = response?.headers["x-paging-pagenumber"];
    console.log(
      `Paginas: ${pages} - Resultados  ${totalResults} - Página Actual  ${actualPage}`
    );
    if (pages > actualPage && pages != 0) {
      moreData = true;
      OFFSET += 1;
    }
    console.log(`Total Ordenes de pago ${totalResults}: Procesando ${LIMIT}`);
    await sync_ordenes_de_pago_page(response.data);
  }
}

export async function sync_detalle_ordenes_de_pago(
  Numero_orden_pago,
  detalles
) {
  let futuros = [];
  let id_detalle = 1;
  for (const detalle of detalles) {
    futuros.push(sync_detalle(Numero_orden_pago, id_detalle, detalle));
    id_detalle += 1;
  }

  await Promise.allSettled(futuros).then((results) =>
    results.forEach((result) => {
      if (result.status != "fulfilled") {
        console.log(`FAILED detalle orden pago: ${result?.reason}`);
      }
    })
  );
  console.log(`Updated Detalle_Orden_Pago`);
}

export async function sync_ordenes_de_pago_page(ordenes_de_pago) {
  for (const ordenPago of ordenes_de_pago) {
    const nueva_ordenPago = await sync_orden_de_pago(ordenPago);

    if (nueva_ordenPago) {
      console.log(
        ` Orden de Pago: ${ordenPago.Numero_orden_pago} -- Ingresada`
      );
    } else {
      console.log(
        ` Orden de Pago: ${ordenPago.Numero_orden_pago} -- Actualizada`
      );
    }

    await sync_detalle_ordenes_de_pago(
      ordenPago.Numero_orden_pago,
      ordenPago.Detalles
    );
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_ordenes_de_pago();
  process.exit();
}
