import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_date_string } from "./primitives.js";
import knex from "knex";

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const GRUPOS_API_URL = "https://api.q10.com/v1/grupos";
const SEDE_JORNADA_API_URL = "https://api.q10.com/v1/sedesjornadas";

const LIMIT = 800;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

async function sync_grupo(grupo, sede_jornada_id) {
  await knex_conn("Grupo")
    .select()
    .where("id", grupo.Consecutivo)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Grupo").insert({
          id: grupo.Consecutivo,
          Nombre: grupo.Nombre,
          Codigo_coordinador: grupo.Codigo_coordinador,
          Nombre_coordinador: grupo.Nombre_coordinador,
          Periodo_consecutivo: grupo.Consecutivo_periodo,
          Periodo_nombre: grupo.Nombre_periodo,
          Codigo_nivel: grupo.Codigo_nivel,
          Nombre_nivel: grupo.Nombre_nivel,
          Programa_codigo: grupo.Codigo_programa,
          Programa_nombre: grupo.Nombre_programa,
          Estado: grupo.Estado,
          Sede_jornada_consecutivo: sede_jornada_id,
        });
      } else {
        await knex_conn("Grupo")
          .update({
            Nombre: grupo.Nombre,
            Codigo_coordinador: grupo.Codigo_coordinador,
            Nombre_coordinador: grupo.Nombre_coordinador,
            Periodo_consecutivo: grupo.Consecutivo_periodo,
            Periodo_nombre: grupo.Nombre_periodo,
            Codigo_nivel: grupo.Codigo_nivel,
            Nombre_nivel: grupo.Nombre_nivel,
            Programa_codigo: grupo.Codigo_programa,
            Programa_nombre: grupo.Nombre_programa,
            Estado: grupo.Estado,
            Sede_jornada_consecutivo: sede_jornada_id,
          })
          .where("id", grupo.Consecutivo);
      }
    });
}

async function split_and_sync_grupos(grupos, programa_codigo) {
  let futuros = [];
  for (const i in grupos) {
    const preinscripcion = grupos[i];

    let fixed_preinscripcion = preinscripcion;
    for (const j in preinscripcion.Programas) {
      const programa = preinscripcion.Programas[j];
      fixed_preinscripcion.Programa_nombre = programa.Nombre_programa;
      fixed_preinscripcion.Programa_codigo = programa_codigo;
      fixed_preinscripcion.Periodo_nombre = programa.Nombre_periodo;
      fixed_preinscripcion.Sede_jornada_nombre = programa.Nombre_sedejornada;
    }
    futuros.push(sync_preinscripcion(fixed_preinscripcion));
  }

  await Promise.allSettled(futuros).then((results) =>
    results.forEach((result) => {
      if (result.status === "fulfilled") {
        log(`${programa_codigo}`);
      } else {
        logError(`FAILED PREINSCRIPCION: ${result?.reason}`);
      }
    })
  );
}

export async function list_sede_jornadas() {
  let sede_jornadas = [];
  try {
    let response = undefined;

    while (true) {
      try {
        // Fetch all courses

        response = await axios.get(SEDE_JORNADA_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      sede_jornadas.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  }
  return sede_jornadas;
}

export async function get_grupos(sede_jornada) {
  let grupos = [];
  OFFSET = 1;
  let pages = undefined;
  let actualPage = undefined;
  let n_items = undefined;
  const req_link = GRUPOS_API_URL + `?Consecutivo_sede_jornada=${sede_jornada}`;

  log(req_link);
  const instance = axios.create({
    baseURL: req_link,
    timeout: 50000,
    headers: { "API-Key": q10_api_key },
  });

  let response = undefined;
  while (true) {
    try {
      response = await instance.get(req_link, {
        params: {
          Limit: LIMIT,
          Offset: OFFSET,
        },
      });
    } catch (error) {
      () => {};
    }
    if (response?.status != 200) {
      () => {};
    } else {
      pages = response.headers["x-paging-pagecount"];
      actualPage = response.headers["x-paging-pagenumber"];
      n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      grupos.push(...structuredClone(response.data));
    }

    // This API always says it has 0 items, so I'll be breaking after the frist page:
    if (n_items == 0) {
      break;
    } else {
      OFFSET += 1;
    }

    if (pages == actualPage) {
      break;
    } else {
      OFFSET += 1;
    }
  }

  return grupos;
}

export async function sync_grupos() {
  log("Get programas");
  const sede_jornadas = await list_sede_jornadas();

  for (const k in sede_jornadas) {
    const sede_jornada_id = sede_jornadas[k].Consecutivo;
    const grupos = await get_grupos(sede_jornada_id);

    if (grupos.length > 0) {
      log(`FOUND  grupos: ${grupos.length}`);
      for (const j in grupos) {
        await sync_grupo(grupos[j], sede_jornada_id);
      }
    }
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_grupos();
  // const grupos = await get_grupos(14, 1077, "TI01-CLSA");
  // await split_and_sync_grupos(grupos, "TI01-CLSA");

  process.exit();
}
