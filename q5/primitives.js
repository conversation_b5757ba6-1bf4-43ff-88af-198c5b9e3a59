import axios from "axios";

export function to_cr_time_string(datetime) {
  return new Date(
    new Date(datetime).toLocaleString("en-US", {
      timeZone: "America/Costa_Rica",
    })
  ).toISOString();
}

export function to_date_string(in_date) {
  return new Date(in_date).toISOString();
}

export async function get_token(usr, psw) {
  let response = await axios.post(
    "https://api.clientify.net/v1/api-auth/obtain_token/",
    {
      username: usr,
      password: psw,
    }
  );

  if (response.status != 200) {
    console.log(error);
    throw error;
  }
  return response.data.token;
}

export async function close_connection(api_key) {
  const header = {
    Authorization: `Token ${api_key}`,
    "Content-Type": "application/json",
  };

  await axios
    .post(
      "https://api.clientify.net/v1/api-auth/logout/",
      {},
      { headers: header }
    )
    .then(function (response) {
      if (response.status != 200) {
        console.error(response);
      }
      return;
    })
    .catch(function (error) {
      console.log(error);
      throw error;
    });
}
