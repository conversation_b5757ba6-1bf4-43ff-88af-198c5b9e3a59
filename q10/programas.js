import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_date_string } from "./primitives.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const PROGRAMAS_API_URL = "https://api.q10.com/v1/programas";
const LIMIT = 800;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

async function sync_programa(programa) {
  await knex_conn("Programa")
    .select()
    .where("id", programa.Codigo)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Programa").insert({
          id: programa.Codigo,
          Nombre: programa.Nombre,
          Abreviatura: programa.Abreviatura,
          Numero_resolucion: programa.Numero_resolucion,
          Fecha_resolucion: programa.Fecha_resolucion,
          Aplica_preinscripcion: programa.Aplica_preinscripcion,
          Aplica_grupo: programa.Aplica_grupo,
          Tipo_evaluacion: programa.Tipo_evaluacion,
          Estado: programa.Estado,
        });
      } else {
        await knex_conn("Programa")
          .update({
            Nombre: programa.Nombre,
            Abreviatura: programa.Abreviatura,
            Numero_resolucion: programa.Numero_resolucion,
            Fecha_resolucion: programa.Fecha_resolucion,
            Aplica_preinscripcion: programa.Aplica_preinscripcion,
            Aplica_grupo: programa.Aplica_grupo,
            Tipo_evaluacion: programa.Tipo_evaluacion,
            Estado: programa.Estado,
          })
          .where("id", programa.Codigo);
      }
    });
}

export async function sync_programas() {
  await sync_programas_estado(true);
  OFFSET = 1;
  await sync_programas_estado(false);
}
export async function sync_programas_estado(estado) {
  try {
    let response = undefined;
    while (true) {
      try {
        // Fetch all courses
        const req_link = PROGRAMAS_API_URL + `?Estado=${estado}`;
        console.log(req_link);
        response = await axios.get(req_link, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
            Estado: false
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        console.log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      console.log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      // This won't work with this particular API point, the offset doesn't work.
      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }

      let futuros = [];
      for (const programa of response.data) {
        console.log(`${programa.Codigo}`);
        futuros.push(sync_programa(programa));
      }

      let results = undefined;
      try {
        results = await Promise.allSettled(futuros);
      } catch (error) {
        console.log("At least 1 program failed to sync.");
      }
      // Record the ones that failed.
      const failed_programas = results.filter((p) => p.status === "rejected");
      for (const i in failed_programas) {
        const failed_programa = failed_programas[i];
        console.log(`FAILED programa: ${failed_programa?.reason}`);
      }
      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    console.error("Error:", error);
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_programas();
  process.exit();
}
