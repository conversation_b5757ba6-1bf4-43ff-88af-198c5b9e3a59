import axios from "axios";
import { config } from "./config.js";
import knex from "knex";
const knex_conn = knex({
    client: "mssql",
    connection: config,
});

 PAGOS PENDIENTES BOSTON NO LO USA POR LA CONFIGURACION DE q10
/*
// API URL
const API_URL = "https://api.q10.com/v1/pagosPendientes";
const LIMIT = 900;
let OFFSET = 0;
let moreData = true;

async function fetchDataAndStoreInDatabase() {
    try {
        const periods = await knex_conn("periods").select("Consecutivo");

        for (const period of periods) {
            // Fetch data from the API for each period
            let response = undefined;
            // if (period.Consecutivo != 11) {
            //     //This is for only doing certain period
            //     continue;
            // }
            moreData = true;
            while (moreData) {
                //Get that at least once, if more data is avail, do it again
                moreData = false;
                try {
                    response = await axios.get(API_URL, {
                        params: {
                            Limit: LIMIT,
                            Offset: OFFSET,
                            Consecutivo_periodo: period.Consecutivo,
                        },
                        headers: {
                            "API-Key": "q10_api_key",
                        },
                    });
                } catch (error) {
                    console.log(
                        "Periodo no tiene creditos: #",
                        period.Consecutivo
                    );
                    continue;
                }
                if (response?.status != 200) {
                    continue;
                }
                const pages = response.headers["x-paging-pagecount"];
                const totalResults =
                    response.headers["x-paging-totalitemcount"];
                const actualPage =
                    response.headers["x-paging-x-paging-pagenumber"];
                if (pages > actualPage && pages != 0) {
                    moreData = true;
                }
                const data = response.data;
                console.log(
                    `Periodo con ${data.length} pagos pendientes: #${period.Consecutivo}`
                );
                // console.log(JSON.stringify(data));
                // Insert data into the database
                await knex_conn.transaction(async (trx) => {
                    for (const pagoEstudiante of data) {
                        const estudianteExists = await trx(
                            "Detalle_Pago_Pendiente"
                        )
                            .select(["Consecutivo_pago_pendiente"])
                            .where(
                                "Codigo_persona",
                                pagoEstudiante.Codigo_persona
                            )
                            .first();
                        const detallesPago = pagoEstudiante.Detalles;
                        delete pagoEstudiante.Detalles;
                        if (!estudianteExists) {
                            console.log(
                                `Nuevo estudiante ${pagoEstudiante.Nombre_completo}`
                            );
                            await knex_conn("Estudiante").insert(
                                pagoEstudiante
                            );
                        }

                        for (const pago of detallesPago) {
                            const existsPago = await trx(
                                "Detalle_Pago_Pendiente"
                            )
                                .select([
                                    "Consecutivo_pago_pendiente",
                                    "Valor_pagado",
                                    "Valor_saldo",
                                ])
                                .where(
                                    "Consecutivo_pago_pendiente",
                                    pago.Consecutivo_pago_pendiente
                                )
                                .first();
                            if (!existsPago) {
                                console.log(
                                    `Nuevo pago ${pago.Consecutivo_pago_pendiente}`
                                );
                                await knex_conn.transaction(async (trx) => {
                                    // Insert into pago related table
                                    const descuentos = pago.Descuentos;
                                    delete pago.Descuentos;
                                    await trx("Detalle_Pago_Pendiente").insert({
                                        ...pago,
                                        Codigo_persona:
                                            pagoEstudiante.Codigo_persona,
                                    });
                                    // Insert into descuentos related table
                                    for (const descuento of descuentos) {
                                        await trx(
                                            "Descuento_Pagos_Pendientes"
                                        ).insert({
                                            ...descuento,
                                            Consecutivo_pago_pendiente:
                                                pago.Consecutivo_pago_pendiente,
                                        });
                                    }
                                });
                            } else {
                                const updateObject = {};
                                if (
                                    existsPago.Valor_pagado != pago.Valor_pagado
                                ) {
                                    updateObject.Valor_pagado =
                                        pago.Valor_pagado;
                                }
                                if (
                                    existsPago.Valor_saldo != pago.Valor_saldo
                                ) {
                                    updateObject.Valor_saldo = pago.Valor_saldo;
                                }
                                if (Object.entries(updateObject).length > 0) {
                                    // console.log(existingOrdenPago, ordenPago);
                                    const existingCreditos = await knex_conn(
                                        "Detalle_Pago_Pendiente"
                                    )
                                        .where(
                                            "Consecutivo_pago_pendiente",
                                            pago.Consecutivo_pago_pendiente
                                        )
                                        .update(updateObject);
                                    console.log(
                                        ` Pago #${pago.Consecutivo_pago_pendiente}- Actualizada`
                                    );
                                }
                            }
                        }
                    }
                });
            }
        }

        console.log("Data stored in the database.");
    } catch (error) {
        console.error("Error:", error);
    } finally {
        // Destroy the connection pool
        await knex_conn.destroy();
    }
}

// Call the function to start fetching and storing data
fetchDataAndStoreInDatabase();

*/