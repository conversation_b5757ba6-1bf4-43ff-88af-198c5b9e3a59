import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_date_string } from "./primitives.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const SEDE_JORNADA_API_URL = "https://api.q10.com/v1/sedesjornadas";
const LIMIT = 800;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

async function sync_sede_jornada(sede_jornada) {
  await knex_conn("Sede_jornada")
    .select()
    .where("id", sede_jornada.Consecutivo)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Sede_jornada").insert({
          id: sede_jornada.Consecutivo,
          Codigo_sede: sede_jornada.Codigo_sede,
          Nombre_sede: sede_jornada.Nombre_sede,
          Codigo_jornada: sede_jornada.Codigo_jornada,
          Nombre_jornada: sede_jornada.Nombre_jornada,
          Sede_jornada: sede_jornada.Sede_jornada,
          Estado: sede_jornada.Estado,
        });
      } else {
        await knex_conn("Sede_jornada")
          .update({
            Codigo_sede: sede_jornada.Codigo_sede,
            Nombre_sede: sede_jornada.Nombre_sede,
            Codigo_jornada: sede_jornada.Codigo_jornada,
            Nombre_jornada: sede_jornada.Nombre_jornada,
            Sede_jornada: sede_jornada.Sede_jornada,
            Estado: sede_jornada.Estado,
          })
          .where("id", sede_jornada.Consecutivo);
      }
    });
}

export async function sync_sede_jornadas() {
  try {
    let response = undefined;
    while (true) {
      try {
        // Fetch all sede_jornada
        response = await axios.get(SEDE_JORNADA_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        console.log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      console.log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      // This won't work with this particular API point, the offset doesn't work.
      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }

      let futuros = [];
      for (const sede_jornada of response.data) {
        console.log(sede_jornada);
        futuros.push(sync_sede_jornada(sede_jornada));
      }

      let results = undefined;
      try {
        results = await Promise.allSettled(futuros);
      } catch (error) {
        console.log("At least 1 program failed to sync.");
      }
      // Record the ones that failed.
      const failed_sede_jornadas = results.filter(
        (p) => p.status === "rejected"
      );
      for (const i in failed_sede_jornadas) {
        const failed_sede_jornada = failed_sede_jornadas[i];
        console.log(`FAILED sede_jornada: ${failed_sede_jornada?.reason}`);
      }
      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    console.error("Error:", error);
  } finally {
    // Destroy the connection pool
    await knex_conn.destroy();
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_sede_jornadas();
  process.exit();
}
