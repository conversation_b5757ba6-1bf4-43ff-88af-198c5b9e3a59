import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_date_string } from "./primitives.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const PROGRAMAS_API_URL = "https://api.q10.com/v1/programas";
const PERIODOS_API_URL = "https://api.q10.com/v1/periodos";
const SEDE_JORNADA_API_URL = "https://api.q10.com/v1/sedesjornadas";
const PREINSCRIPCIONES_API_URL = "https://api.q10.com/v1/preinscripciones";

const LIMIT = 800;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

async function sync_preinscripcion(preinscripcion) {
  await knex_conn("Preinscripcion")
    .select()
    .where("id", preinscripcion.Consecutivo)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Preinscripcion").insert({
          id: preinscripcion.Consecutivo,
          Programa_codigo: preinscripcion.Programa_codigo,
          Programa_nombre: preinscripcion.Programa_nombre,
          Periodo_nombre: preinscripcion.Periodo_nombre,
          Sede_jornada_nombre: preinscripcion.Sede_jornada_nombre,
          Fecha_preinscripcion: preinscripcion.Fecha_preinscripcion,
          Primer_nombre: preinscripcion.Primer_nombre,
          Primer_apellido: preinscripcion.Primer_apellido,
          Segundo_apellido: preinscripcion.Segundo_apellido,
          Codigo_tipo_identificacion: preinscripcion.Codigo_tipo_identificacion,
          id_estudiante: preinscripcion.Numero_identificacion,
          Genero: preinscripcion.Genero,
          Fecha_nacimiento: preinscripcion.Fecha_nacimiento,
          Telefono: preinscripcion.Telefono,
          Celular: preinscripcion.Celular,
          Email: preinscripcion.Email,
          Direccion: preinscripcion.Direccion,
          Lugar_residencia: preinscripcion.Lugar_residencia,
        });
      } else {
        await knex_conn("Preinscripcion")
          .update({
            Programa_codigo: preinscripcion.Programa_codigo,
            Programa_nombre: preinscripcion.Programa_nombre,
            Periodo_nombre: preinscripcion.Periodo_nombre,
            Sede_jornada_nombre: preinscripcion.Sede_jornada_nombre,
            Fecha_preinscripcion: preinscripcion.Fecha_preinscripcion,
            Primer_nombre: preinscripcion.Primer_nombre,
            Primer_apellido: preinscripcion.Primer_apellido,
            Segundo_apellido: preinscripcion.Segundo_apellido,
            Codigo_tipo_identificacion:
              preinscripcion.Codigo_tipo_identificacion,
            id_estudiante: preinscripcion.Numero_identificacion,
            Genero: preinscripcion.Genero,
            Fecha_nacimiento: preinscripcion.Fecha_nacimiento,
            Telefono: preinscripcion.Telefono,
            Celular: preinscripcion.Celular,
            Email: preinscripcion.Email,
            Direccion: preinscripcion.Direccion,
            Lugar_residencia: preinscripcion.Lugar_residencia,
          })
          .where("id", preinscripcion.Consecutivo);
      }
    });
}

async function split_and_sync_preinscripciones(
  preinscripciones,
  programa_codigo
) {
  let futuros = [];
  for (const i in preinscripciones) {
    const preinscripcion = preinscripciones[i];

    let fixed_preinscripcion = preinscripcion;
    for (const j in preinscripcion.Programas) {
      const programa = preinscripcion.Programas[j];
      fixed_preinscripcion.Programa_nombre = programa.Nombre_programa;
      fixed_preinscripcion.Programa_codigo = programa_codigo;
      fixed_preinscripcion.Periodo_nombre = programa.Nombre_periodo;
      fixed_preinscripcion.Sede_jornada_nombre = programa.Nombre_sedejornada;
    }
    futuros.push(sync_preinscripcion(fixed_preinscripcion));
  }

  await Promise.allSettled(futuros).then((results) =>
    results.forEach((result) => {
      if (result.status === "fulfilled") {
        log(`${programa_codigo}`);
      } else {
        logError(`FAILED PREINSCRIPCION: ${result?.reason}`);
      }
    })
  );
}

export async function list_sede_jornadas() {
  let sede_jornadas = [];
  try {
    let response = undefined;

    while (true) {
      try {
        // Fetch all courses

        response = await axios.get(SEDE_JORNADA_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      sede_jornadas.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  }
  return sede_jornadas;
}

export async function list_programas() {
  let programas = [];
  try {
    let response = undefined;

    while (true) {
      try {
        // Fetch all courses

        response = await axios.get(PROGRAMAS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      programas.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  }
  return programas;
}

export async function list_periodos() {
  OFFSET = 1;
  let periodos = [];
  try {
    let response = undefined;
    while (true) {
      try {
        // Fetch all courses
        response = await axios.get(PERIODOS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      periodos.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  }

  return periodos;
}

export async function list_cursos() {
  OFFSET = 1;
  let cursos = [];
  try {
    let response = undefined;
    while (true) {
      try {
        // Fetch all courses
        response = await axios.get(CURSOS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      cursos.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  }
  return cursos;
}

export async function get_preinscripciones_periodo(periodo) {
  let preinscripciones = [];
  OFFSET = 0;
  const req_link = PREINSCRIPCIONES_API_URL + `?Periodo=${periodo}`;

  log(req_link);
  const instance = axios.create({
    baseURL: req_link,
    timeout: 50000,
    headers: { "API-Key": q10_api_key },
  });

  try {
    let response = undefined;
    while (true) {
      try {
        // response = await instance.get(req_link, {
        //   params: {
        //     Limit: LIMIT,
        //     Offset: OFFSET,
        //   },
        // });

        response = await axios.get(req_link, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        break;
      }
      if (response?.status != 200) {
        break;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      log(response.data);
      preinscripciones.push(...structuredClone(response.data));

      // This API always says it has 0 items, so I'll be breaking after the frist page:
      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }

      if (pages == actualPage) {
        break;
      } else {
        OFFSET += 1;
      }
    }
  } catch (error) {
    log(error);
  }
  return preinscripciones;
}

export async function get_preinscripciones(
  sede_jornada,
  periodo,
  programa,
  wait
) {
  let preinscripciones = [];
  OFFSET = 1;
  const req_link =
    PREINSCRIPCIONES_API_URL +
    `?Sede_jornada=${sede_jornada}` +
    `&Periodo=${periodo}` +
    `&Programa=${programa}`;

  log(req_link);
  const instance = axios.create({
    baseURL: req_link,
    timeout: 50000,
    headers: { "API-Key": q10_api_key },
  });

  try {
    let response = undefined;
    while (true) {
      try {
        response = await instance.get(req_link, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
        });
      } catch (error) {
        log(error);
        break;
      }
      if (response?.status != 200) {
        break;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      preinscripciones.push(...structuredClone(response.data));

      // This API always says it has 0 items, so I'll be breaking after the frist page:
      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }

      if (pages == actualPage) {
        break;
      } else {
        OFFSET += 1;
      }
    }
  } catch (error) {
    log(error);
  }
  return preinscripciones;
}

async function sync_preinscripciones_from_sjpp(
  sede_jornadas,
  periodos,
  programas
) {
  for (const j in periodos) {
    log(periodos[j]);
    for (const k in programas) {
      let preinscripciones = [];
      for (const i in sede_jornadas) {
        preinscripciones.push(
          ...structuredClone(
            await get_preinscripciones(
              sede_jornadas[i].Consecutivo,
              periodos[j].Consecutivo,
              programas[k].Codigo,
              true
            )
          )
        );
      }

      if (preinscripciones.length > 0) {
        log(`FOUND  preinscripciones: ${preinscripciones.length}`);
        await split_and_sync_preinscripciones(
          preinscripciones,
          programas[k].Codigo
        );
      }
    }
    log("------");
  }
}

async function sync_preinscripciones_periodo(periodos) {
  for (const i in periodos) {
    const preinscripciones = get_preinscripciones_periodo(
      periodos[i].Consecutivo
    );

    if (preinscripciones.length > 0) {
      log(`Syncing ${preinscripciones.length} preinscripciones`);
      await split_and_sync_preinscripciones(preinscripciones, "");
    }
  }
  log("------");
}

export async function sync_preinscripciones() {
  // console.log("Get programas");
  // const programas = await list_programas();
  log("Get periodos");
  const periodos = await list_periodos();
  // console.log("Get sede_jornadas");
  // const sede_jornadas = await list_sede_jornadas();

  await sync_preinscripciones_periodo(periodos);
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_preinscripciones();
  // const preinscripciones = await get_preinscripciones(14, 1077, "TI01-CLSA");
  // await split_and_sync_preinscripciones(preinscripciones, "TI01-CLSA");

  process.exit();
}
