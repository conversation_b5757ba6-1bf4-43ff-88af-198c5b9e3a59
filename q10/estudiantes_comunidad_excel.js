import structuredClone from "@ungap/structured-clone";
import axios from "axios";
import { config, q10_api_key } from "./config.js";
import knex from "knex";
import async, { mapLimit } from "async";
import { log, logError } from "../utils/logging.js";
// Import yargs
import yargs from "yargs";
import { hideBin } from "yargs/helpers"; // Helper to process argv

const knex_conn = knex({
  client: "mssql",
  connection: config,
  pool: {
    min: 0,
    max: 10,
    idleTimeoutMillis: 60000, // 1 minute idle timeout
    acquireTimeoutMillis: 60000, // 1 minute acquire timeout
    createTimeoutMillis: 30000, // 30 seconds create timeout
    createRetryIntervalMillis: 200, // Time between retries
  },
});

// API URL
const ESTUDIANTES_API_URL =
  "https://api.q10.com/v1/comunidad-excel/estudiantes";
// Define a concurrency limit - Start lower if deadlocks persist
const SYNC_CONCURRENCY_LIMIT = 1; // Set to 1 to avoid deadlocks if they are frequent
const MAX_DEADLOCK_RETRIES = 3;
const DEADLOCK_RETRY_DELAY_MS = 150;
const PROGRESS_LOG_INTERVAL = 10; // Log progress every 10 records

// Helper function to introduce delay
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Function to check if an error is a deadlock error (MSSQL specific)
function isDeadlockError(error) {
  return (
    error?.number === 1205 || (error?.message || "").includes("deadlock victim")
  );
}

// Modified sync_estudiante to accept insertOnly flag
export async function sync_estudiante(estudiante, insertOnly = false) {
  let action = "unknown"; // To store if it was an insert, update, or skip
  let trx; // Define trx outside the try block to potentially use in catch if needed
  try {
    const rows = await knex_conn("Estudiante_comunidad_excel")
      .select("Codigo_estudiante")
      .where("Codigo_estudiante", estudiante.Codigo_estudiante);

    const estudianteData = structuredClone(estudiante);

    // Ensure Informacion_matricula is an array before accessing
    const informacion_matricula = Array.isArray(
      estudianteData.Informacion_matricula
    )
      ? estudianteData.Informacion_matricula
      : [];

    estudianteData.Estado_matricula =
      informacion_matricula[0]?.Estado_matricula || null;

    // Clean up properties not needed in the main table
    delete estudianteData.Informacion_adicional; // Still delete even if not used below, for consistency
    delete estudianteData.Informacion_matricula;
    delete estudianteData.Informacion_academica;
    delete estudianteData.Informacion_laboral;
    delete estudianteData.Preguntas_personalizadas;
    delete estudianteData.Familiares;

    if (rows.length === 0) {
      action = "inserted"; // Mark action as insert
      log(
        `Codigo_estudiante: ${estudianteData.Codigo_estudiante} No encontrado, agregando`
      );
      trx = await knex_conn.transaction();
      try {
        await trx
          .insert({ ...estudianteData })
          .into("Estudiante_comunidad_excel");
        // Only insert matricula info if it exists
        if (informacion_matricula.length > 0) {
          await Promise.all(
            informacion_matricula.map((info_matricula) =>
              trx
                .insert({
                  Codigo_estudiante: estudianteData.Codigo_estudiante,
                  ...info_matricula,
                })
                .into("Comunidad_excel_informacion_matricula")
            )
          );
        }
        await trx.commit();
      } catch (err) {
        await trx.rollback();
        throw err; // Re-throw after rollback
      }
    } else {
      // --- Insert Only Check ---
      if (insertOnly) {
        action = "skipped";
        log(
          `Codigo_estudiante: ${estudianteData.Codigo_estudiante} - Existe, SKIPPING update (--insert-only mode)`
        );
      } else {
        action = "updated"; // Mark action as update
        log(
          `Codigo_estudiante: ${estudianteData.Codigo_estudiante} - Existe, Actualizando informacion_matriculas - Estado ${estudianteData.Estado_matricula}`
        );

        // Create a new transaction
        // trx = await knex_conn.transaction();

        try {
          // Insert new matricula info if it exists
          if (informacion_matricula.length > 0) {
            // --- Sequential Insertion ---
            // log(
            //   ` -> [${estudianteData.Codigo_estudiante}] Inserting/Update new matricula records for ${informacion_matricula.length} program(s) within transaction...`
            // );
            for (const info_matricula of informacion_matricula) {
              try {
                const matriculaData = await knex_conn(
                  "Comunidad_excel_informacion_matricula"
                )
                  .select("Estado_matricula")
                  .where("Codigo_estudiante", estudianteData.Codigo_estudiante)
                  .where("Nombre_programa", info_matricula.Nombre_programa);
                if (matriculaData.length > 0) {
                  if (
                    matriculaData.Estado_matricula !==
                    info_matricula.Estado_matricula
                  ) {
                    log(
                      ` -> [${estudianteData.Codigo_estudiante}] Matricula exists for program [${info_matricula.Nombre_programa}], updating...`
                    );
                    await knex_conn("Comunidad_excel_informacion_matricula")
                      .update({
                        ...info_matricula,
                      })
                      .where(
                        "Codigo_estudiante",
                        estudianteData.Codigo_estudiante
                      )
                      .where("Nombre_programa", info_matricula.Nombre_programa);
                  }
                } else {
                  await knex_conn(
                    "Comunidad_excel_informacion_matricula"
                  ).insert({
                    Codigo_estudiante: estudianteData.Codigo_estudiante,
                    ...info_matricula,
                  });

                  log(
                    ` -> [${estudianteData.Codigo_estudiante}] Insertion complete. Fecha Matricula ${info_matricula.Fecha_matricula}`
                  );
                }
              } catch (insertErr) {
                action = "skipped";
                log(
                  ` -> [${estudianteData.Codigo_estudiante}] Insert failed for program [${info_matricula.Nombre_programa}]: ${insertErr.message}`
                );
                // Continue with next record rather than failing entire transaction
              }
            }
          }
          await knex_conn("Estudiante_comunidad_excel")
            .update({ ...estudianteData })
            .where("Codigo_estudiante", estudiante.Codigo_estudiante);
        } catch (err) {
          throw err; // Re-throw after rollback
        }
      }
    }
    return { status: action }; // Return status on success ('inserted', 'updated', 'skipped')
  } catch (error) {
    // Log the error with context
    logError(
      `Error syncing Codigo_estudiante ${estudiante?.Codigo_estudiante} (intended action: ${action}):`,
      error
    );
    // Re-throw the error so the retry wrapper can catch it
    throw error;
  }
}

// Modified wrapper to accept and pass insertOnly flag
async function sync_estudiante_with_retry(estudiante, insertOnly = false) {
  let retries = MAX_DEADLOCK_RETRIES;
  while (retries >= 0) {
    try {
      // Pass insertOnly flag to sync_estudiante
      const result = await sync_estudiante(estudiante, insertOnly);
      return result; // Return { status: 'inserted'/'updated'/'skipped' } on success
    } catch (error) {
      if (isDeadlockError(error) && retries > 0) {
        retries--;
        log(
          `Deadlock detected for ${estudiante?.Codigo_estudiante}. Retrying in ${DEADLOCK_RETRY_DELAY_MS}ms. Retries left: ${retries}`
        );
        await delay(DEADLOCK_RETRY_DELAY_MS);
      } else {
        logError(
          `Failed to sync estudiante ${estudiante?.Codigo_estudiante} after ${
            MAX_DEADLOCK_RETRIES + 1
          } attempts or due to non-deadlock error.`,
          error
        );
        return { status: "failed", error: error };
      }
    }
  }
  logError(
    `Exhausted retry logic unexpectedly for ${estudiante?.Codigo_estudiante}`
  );
  return {
    status: "failed",
    error: new Error("Retry logic failed unexpectedly"),
  };
}

export async function get_estudiantes(
  startDate = "2025-01-01",
  endDate = null
) {
  // ... (get_estudiantes function remains the same)
  const fecha = new Date();
  const today = endDate || fecha.toISOString().split("T")[0];
  let estudiantes = [];
  const LIMIT = 9000;
  let OFFSET = 1;
  const estadosEstudiante = ["T"];
  let moreData = true;

  for (const estadoEstudiante of estadosEstudiante) {
    OFFSET = 1;
    moreData = true;

    const baseUrl = ESTUDIANTES_API_URL;
    const baseParams = {
      Fecha_inicio_matricula: startDate,
      Fecha_fin_matricula: today,
      Incluir_info_adicional: "false", // Set to false if not needed in sync_estudiante
      Incluir_info_matricula: "true", // Keep true
      Incluir_info_academica: "false",
      Incluir_info_laboral: "false",
      Incluir_preguntas_personalizadas: "false",
      Incluir_info_familiares: "false",
      Estado: estadoEstudiante,
      Limit: LIMIT,
    };

    log(
      `Fetching Comunidad Excel for Estado: ${estadoEstudiante}, Start: ${startDate}, End: ${today}`
    );

    let baseTimeout = 90000;

    while (moreData) {
      let retries = 2;
      let success = false;
      let response = undefined;

      const currentParams = { ...baseParams, Offset: OFFSET };
      log(`Requesting page with OFFSET: ${OFFSET}`);

      while (retries >= 0 && !success) {
        try {
          const instance = axios.create({
            timeout: baseTimeout,
            headers: { "API-Key": q10_api_key },
          });
          response = await instance.get(baseUrl, { params: currentParams });
          success = true;
        } catch (error) {
          if (error.code === "ECONNABORTED" && retries > 0) {
            baseTimeout += 20000;
            log(
              `Connection timeout. Retrying with timeout ${baseTimeout}ms. Retries left: ${retries}`
            );
            retries--;
            await delay(1000);
          } else {
            logError(
              `API request failed for offset ${OFFSET}:`,
              error.response?.status,
              error.message,
              error.code
            );
            moreData = false;
            success = false;
            break;
          }
        }
      }

      if (!success) {
        log(
          `Failed to fetch data for offset ${OFFSET} after retries. Stopping for estado ${estadoEstudiante}.`
        );
        moreData = false;
        break;
      }

      if (response?.status !== 200) {
        log(
          `Received non-200 status ${response?.status} for offset ${OFFSET}. Stopping for estado ${estadoEstudiante}.`
        );
        moreData = false;
        break;
      }

      const pages = parseInt(response.headers["x-paging-pagecount"] || "0", 10);
      const actualPage = parseInt(
        response.headers["x-paging-pagenumber"] || "0",
        10
      );
      const n_items_on_page = response.data?.length || 0;
      const total_items = parseInt(
        response.headers["x-paging-totalitemcount"] || "0",
        10
      );

      log(
        `Estado ${estadoEstudiante}: Page ${actualPage}/${pages}. Items on page: ${n_items_on_page}. Total items: ${total_items}. Offset: ${OFFSET}`
      );

      if (response.data && response.data.length > 0) {
        estudiantes.push(...structuredClone(response.data));
      }

      if (actualPage >= pages || n_items_on_page === 0) {
        log(
          `Reached last page (${actualPage}) or empty page for estado ${estadoEstudiante}.`
        );
        moreData = false;
      } else {
        OFFSET += 1;
      }
    }
  }

  log(`Total estudiantes fetched: ${estudiantes.length}`);
  return estudiantes;
}

// --- Main execution block ---
(async () => {
  const mainModuleUrl = new URL(import.meta.url);
  const entryScriptPath = process.argv[1];

  if (
    mainModuleUrl.pathname === entryScriptPath ||
    mainModuleUrl.pathname.split("/").pop() === entryScriptPath.split("/").pop()
  ) {
    // --- Argument Parsing with yargs ---
    const argv = yargs(hideBin(process.argv))
      .option("startDate", {
        alias: "s",
        type: "string",
        description:
          "Start date for fetching students (YYYY-MM-DD) - Default 2025-01-01",
        default: "2025-01-01",
      })
      .option("endDate", {
        alias: "e",
        type: "string",
        description:
          "End date for fetching students (YYYY-MM-DD). Defaults to today if not provided.",
        default: null, // Explicitly null, will be handled in get_estudiantes
      })
      .option("insertOnly", {
        alias: "i",
        type: "boolean",
        description: "Only insert new records, skip updates for existing ones.",
        default: false,
      })
      .usage("Usage: $0 [options]") // Optional: Add usage instruction
      .help() // Enable --help flag
      .alias("help", "h").argv; // Parse the arguments

    // Use the parsed arguments directly
    const { startDate, endDate, insertOnly } = argv;
    // --- End Argument Parsing ---

    let exitCode = 0;
    let results = [];
    // --- Counters for Progress ---
    let processedCount = 0;
    let insertedCount = 0;
    let updatedCount = 0;
    let skippedCount = 0;
    let failedCount = 0;
    // --- End Counters ---

    try {
      log(
        `Starting direct execution: Fetching estudiantes from ${startDate} to ${
          endDate || "today"
        }`
      );
      if (insertOnly) {
        log("Running in --insert-only mode. Existing records will be skipped.");
      }
      // Pass parsed arguments to get_estudiantes
      const estudiantes = await get_estudiantes(startDate, endDate);
      const totalEstudiantes = estudiantes.length; // Get total count for progress log

      if (totalEstudiantes > 0) {
        log(
          `Fetched ${totalEstudiantes} estudiantes. Starting sync with concurrency ${SYNC_CONCURRENCY_LIMIT} and ${MAX_DEADLOCK_RETRIES} retries...`
        );
        
        // Pass insertOnly flag to the mapLimit iteratee
        results = await mapLimit(
          estudiantes,
          SYNC_CONCURRENCY_LIMIT,
          async (estudiante) => {
            // Pass the flag to the retry wrapper
            // if(estudiante.Usuario!='115680901'){
            //   return { status: "skipped" };
            // }
            const result = await sync_estudiante_with_retry(
              estudiante,
              insertOnly
            );

            // --- Update Counters and Log Progress ---
            processedCount++;
            switch (result?.status) {
              case "inserted":
                insertedCount++;
                break;
              case "updated":
                updatedCount++;
                break;
              case "skipped":
                skippedCount++;
                break;
              default:
                failedCount++;
                break;
            }

            // Log progress every N records or on the last record
            if (
              processedCount % PROGRESS_LOG_INTERVAL === 0 ||
              processedCount === totalEstudiantes
            ) {
              log(
                `--- Progress (${processedCount}/${totalEstudiantes}) --- Inserted: ${insertedCount}, Updated: ${updatedCount}, Skipped: ${skippedCount}, Failed: ${failedCount}`
              );
            }
            // --- End Progress Logic ---

            return result; // Return result for final summary
          }
        );

        // --- Final Summary (already uses the final counter values) ---
        log("--- Sync Process Final Summary ---");
        log(`Total Processed: ${processedCount}`); // Use processedCount which reflects actual attempts
        log(`Successfully Inserted: ${insertedCount}`);
        log(`Successfully Updated: ${updatedCount}`);
        log(`Skipped (existing in --insert-only mode): ${skippedCount}`);
        log(`Failed: ${failedCount}`);
        log("---------------------------------");

        if (failedCount > 0) {
          exitCode = 1;
          logError(`Sync completed with ${failedCount} failures.`);
        } else {
          log("Sync process completed successfully.");
        }
      } else {
        log("No estudiantes found to sync.");
      }
    } catch (error) {
      logError(
        "A critical error occurred during the main execution sync process:",
        error
      );
      exitCode = 1;
    } finally {
      log("Exiting script.");
      try {
        await knex_conn.destroy();
      } catch (dbError) {
        logError("Error closing database connection:", dbError);
        if (exitCode === 0) exitCode = 1;
      }
      process.exit(exitCode);
    }
  }
})();

// --- Exported function ---
// Modified to accept insertOnly parameter
export async function sync_comunidad_excel(
  startDate = "2005-01-01",
  endDate = null,
  insertOnly = false
) {
  let results = [];
  // --- Counters for Progress ---
  let processedCount = 0;
  let insertedCount = 0;
  let updatedCount = 0;
  let skippedCount = 0;
  let failedCount = 0;
  // --- End Counters ---
  try {
    log(
      `Starting sync_comunidad_excel: Fetching estudiantes from ${startDate} to ${
        endDate || "today"
      }`
    );
    if (insertOnly) {
      log("Running sync_comunidad_excel in insert-only mode.");
    }
    const estudiantes = await get_estudiantes(startDate, endDate);
    const totalEstudiantes = estudiantes.length; // Get total count

    if (totalEstudiantes > 0) {
      log(
        `Fetched ${totalEstudiantes} estudiantes. Starting sync with concurrency ${SYNC_CONCURRENCY_LIMIT} and ${MAX_DEADLOCK_RETRIES} retries...`
      );

      // Pass insertOnly flag to the mapLimit iteratee
      results = await mapLimit(
        estudiantes,
        SYNC_CONCURRENCY_LIMIT,
        async (estudiante) => {
          // Pass the flag to the retry wrapper
          const result = await sync_estudiante_with_retry(
            estudiante,
            insertOnly
          );

          // --- Update Counters and Log Progress ---
          processedCount++;
          switch (result?.status) {
            case "inserted":
              insertedCount++;
              break;
            case "updated":
              updatedCount++;
              break;
            case "skipped":
              skippedCount++;
              break;
            default:
              failedCount++;
              break;
          }

          // Log progress every N records or on the last record
          if (
            processedCount % PROGRESS_LOG_INTERVAL === 0 ||
            processedCount === totalEstudiantes
          ) {
            log(
              `--- Progress (${processedCount}/${totalEstudiantes}) --- Inserted: ${insertedCount}, Updated: ${updatedCount}, Skipped: ${skippedCount}, Failed: ${failedCount}`
            );
          }
          // --- End Progress Logic ---

          return result; // Return result for final summary
        }
      );

      // --- Final Summary ---
      log("--- sync_comunidad_excel Final Summary ---");
      log(`Total Processed: ${processedCount}`);
      log(`Successfully Inserted: ${insertedCount}`);
      log(`Successfully Updated: ${updatedCount}`);
      log(`Skipped (existing in insert-only mode): ${skippedCount}`);
      log(`Failed: ${failedCount}`);
      log("---------------------------------------");

      if (failedCount > 0) {
        logError(
          `sync_comunidad_excel completed with ${failedCount} failures.`
        );
        // Optionally throw an error here if the caller needs to know about failures
        // throw new Error(`Sync process completed with ${failedCount} failures.`);
      } else {
        log("sync_comunidad_excel process completed successfully.");
      }
    } else {
      log("No estudiantes found to sync in sync_comunidad_excel.");
    }
    return results; // Return detailed results array
  } catch (error) {
    logError("An error occurred during sync_comunidad_excel:", error);
    throw error; // Re-throw for the caller
  } finally {
    // Connection destroy is likely handled by the caller or main execution block
  }
}
