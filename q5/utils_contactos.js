import axios from "axios";
import knex from "knex";
import { config, usr, psw } from "./config.js";
import { to_cr_time_string } from "./primitives.js";

import { add_test_contacto } from "./test_contactos.js";

const knex_conn = knex({
  client: "mssql",
  connection: config,
});
const CONTACTS_API_URL =
  "https://api.clientify.net/v1/contacts/:contact_id/addresses/?page_size=200";

const clean_add_contacto = async (contacto) => {
  let clean_contacto = undefined;
  try {
    clean_contacto = fix_contacto(contacto);
  } catch (error) {
    throw error;
  }
  return await add_contacto(clean_contacto);
};

export function fix_contacto(contacto) {
  let fixed_contacto = structuredClone(contacto);

  if (contacto.emails != null) {
    let mail_comma = "";
    for (const mail of contacto.emails) {
      mail_comma += mail.email + ",";
    }
    fixed_contacto.emails = mail_comma.slice(0, -1);
  }

  if (contacto.phones != null) {
    let phone_comma = "";
    for (const phone of contacto.phones) {
      phone_comma += phone.phone + ",";
    }
    fixed_contacto.phones = phone_comma.slice(0, -1);
  }

  if (contacto.company != null) {
    fixed_contacto.company = contacto.company.name;
  }

  if (contacto.contact_source != null) {
    if (fixed_contacto.contact_source.hasOwnProperty("name")) {
      fixed_contacto.contact_source = contacto.contact_source.name;
    }
  }

  if (contacto.contact_type != null) {
    if (fixed_contacto.contact_type.hasOwnProperty("name")) {
      fixed_contacto.contact_type = contacto.contact_type.name;
    }
  }

  if (contacto.tags != null) {
    let tags_comma = "";
    for (const tag of contacto.tags) {
      tags_comma += tag + ",";
    }
    fixed_contacto.tags = tags_comma.slice(0, -1);
  }

  if (contacto.custom_fields != null) {
    let field_str = "";
    for (const field of contacto.custom_fields) {
      field_str += JSON.stringify(field, null, "\n");
    }
    fixed_contacto.custom_fields = field_str;

    // Get also the national ID
    for (const item in contacto.custom_fields) {
      const entry = contacto.custom_fields[item];
      if (entry.field === "0.2 Cedula") {
        const tmp_nbr = Number(entry.value.replace(/ /g, ""));
        if (isNaN(tmp_nbr)) {
          console.error(
            `WARNING: Contact ${
              contacto.id
            } has an invalid Cedula. This may result in an error when trying to sync to the SQL database. ${JSON.stringify(
              entry
            )}`
          );

          fixed_contacto.Cedula = String(entry.value);
        } else {
          // Setting it to a string in the end because it keeps failing due to crappy data
          fixed_contacto.Cedula = String(tmp_nbr);
        }
      }
      if (entry.field === "0.003 Asesor académico histórico") {
        fixed_contacto.AsesorHistorico = String(entry.value);
      }
      if (entry.field === "Periodo actual") {
        fixed_contacto.Periodo = String(entry.value);
      }
      if (entry.field === "0.6 Escuela") {
        fixed_contacto.Escuela = String(entry.value);
      }

      if (entry.field === "0.5 Facultad") {
        fixed_contacto.Facultad = String(entry.value);
      }
      if (entry.field === "0.7 Grado") {
        fixed_contacto.Grado = String(entry.value);
      }
      if (entry.field === "0.8 Carrera de Interes") {
        fixed_contacto.Carrera = String(entry.value);
      }
    }
  }

  if (contacto.addresses == null) {
    Function.prototype();
  } else if (
    typeof contacto.addresses === "string" ||
    contacto.addresses instanceof String
  ) {
    // Sometimes is addresses is an empty string.
    Function.prototype();
  } else {
    let address_str = "";
    for (const [i, address] of contacto.addresses.entries()) {
      let street = `street ${i + 1}: ${address["street"]}, `;
      let city = `city ${i + 1}: ${address["city"]}, `;
      let state = `state ${i + 1}: ${address["state"]}, `;
      let country = `country ${i + 1}: ${address["country"]}, `;
      let postal_code = `postal_code ${i + 1}: ${address["postal_code"]} - `;
      address_str += street + city + state + country + postal_code;
    }
    fixed_contacto.addresses = address_str.slice(0, -3);
  }

  if (contacto.created != null) {
    fixed_contacto.created = to_cr_time_string(contacto.created);
  }
  if (contacto.modified != null) {
    fixed_contacto.modified = to_cr_time_string(contacto.modified);
  }
  if (contacto.last_contact != null) {
    fixed_contacto.last_contact = to_cr_time_string(contacto.last_contact);
  }

  return fixed_contacto;
}

export async function add_contacto(contacto) {
  await knex_conn("Contactos")
    .select()
    .where("Contacto_id", contacto.id)
    .then(async (rows) => {
      if (rows.length === 0) {
        try {
          await knex_conn("Contactos").insert({
            Contacto_id: contacto.id,
            Propietario: contacto.owner_name,
            Propietario_email: contacto.owner,
            Nombres: contacto.first_name,
            Apellidos: contacto.last_name,
            Cargo: contacto.title,
            Empresa: contacto.company,
            Estado: contacto.status,
            Emails: contacto.emails,
            Telefonos: contacto.phones,
            Origen: contacto.contact_source,
            Tipo: contacto.contact_type,
            Origen_web: contacto.medium,
            Custom_fields: contacto.custom_fields,
            Direcciones: contacto.addresses,
            Creado: contacto.created,
            Modificado: contacto.modified,
            Ultimo_contacto: contacto.last_contact,
            Tags: contacto.tags ? contacto.tags : "",
            Periodo: contacto.Periodo ? contacto.Periodo : "",
            Carrera: contacto.Carrera ? contacto.Carrera : "",
            Escuela: contacto.Escuela ? contacto.Escuela : "",
            Facultad: contacto.Facultad ? contacto.Facultad : "",
            Grado: contacto.Grado ? contacto.Grado : "",
            AsesorHistorico: contacto.AsesorHistorico
              ? contacto.AsesorHistorico
              : "",
          });
        } catch (error) {
          console.error("Error updating contact:", error);
        }
      } else {
        try {
          await knex_conn("Contactos")
            .update({
              Propietario: contacto.owner_name,
              Propietario_email: contacto.owner,
              Nombres: contacto.first_name,
              Apellidos: contacto.last_name,
              Cargo: contacto.title,
              Empresa: contacto.company,
              Estado: contacto.status,
              Emails: contacto.emails,
              Telefonos: contacto.phones,
              Origen: contacto.contact_source,
              Tipo: contacto.contact_type,
              Origen_web: contacto.medium,
              Custom_fields: contacto.custom_fields,
              Direcciones: contacto.addresses,
              Creado: contacto.created,
              Modificado: contacto.modified,
              Ultimo_contacto: contacto.last_contact,
              Cedula: contacto.Cedula,
              Tags: contacto.tags ? contacto.tags : "",
              Periodo: contacto.Periodo ? contacto.Periodo : "",
              Carrera: contacto.Carrera ? contacto.Carrera : "",
              Escuela: contacto.Escuela ? contacto.Escuela : "",
              Facultad: contacto.Facultad ? contacto.Facultad : "",
              Grado: contacto.Grado ? contacto.Grado : "",
              AsesorHistorico: contacto.AsesorHistorico
                ? contacto.AsesorHistorico
                : "",
            })
            .where("Contacto_id", contacto.id);
        } catch (error) {
          console.error("Error updating contact:", error);
        }
      }
    });
}

export async function add_page(contacts) {
  let futuros = [];
  // Queue up asynchronous cleaning-addition of all input contacts.
  for (const contacto of contacts) {
    console.log(
      `add_page - ${contacto.first_name} ${contacto.last_name} -- ${contacto.id}`
    );
    let added_contacto = undefined;
    try {
      added_contacto = clean_add_contacto(contacto);
    } catch (error) {
      console.log(`Could not queue contact: ${JSON.stringify(contacto)}`);
      continue;
    }

    futuros.push(added_contacto);
  }
  console.log(`${getISODateTime()} ----`);
  try {
    // Block until all contacts are done.
    await Promise.all(futuros);
  } catch (error) {
    console.error(
      `At least 1 of the previous ${contacts.length} contacts failed to sync.`
    );
    throw error;
  }
  return;
}

export async function get_contact(api_key, contact_id) {
  const CONTACTS_API_URL = `https://api.clientify.net/v1/contacts/${contact_id}/`;
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  try {
    response = await axios.get(CONTACTS_API_URL, { headers: header });
  } catch (error) {
    console.error(error);
    throw error;
  }

  if (response.status == 200) {
    return response.data;
  } else {
    console.error(response);
    return "";
  }
}

export async function add_contacts(api_key) {
  const CONTACTS_API_URL = `https://api.clientify.net/v1/contacts/?page_size=250`;
  const header = {
    Authorization: `Token ${api_key}`,
  };
  console.log(CONTACTS_API_URL, { headers: header });
  await axios
    .get(CONTACTS_API_URL, { headers: header })
    .then(async function (response) {
      if (response.status != 200) {
        console.log(response);
      } else {
        let next_page = "";
        while (next_page != null) {
          next_page = response.data.next;
          // Each page has ~250 contacts and apparently that's too much for the DB.
          // await add_page(response.data.results);

          // So, now we do it with a transaction
          await add_page_trx(response.data.results);
          console.log(`${getISODateTime()}-----------`, next_page);
          if (next_page == null) {
            break;
          }
          response = await axios.get(next_page, { headers: header });
          if (response.status != 200) {
            console.error(
              `${getISODateTime()}-Failed to get next page ${next_page}`,
              resp
            );
            continue;
          }
        }
      }
      return;
    })
    .catch(function (error) {
      console.log(`Failed to get ${CONTACTS_API_URL}`);
      console.log(error);
    });
}

export async function add_page_trx(contact_page) {
  let clean_contactos = [];
  for (const contacto of contact_page) {
    const clean_contacto = fix_contacto(contacto);
    clean_contactos.push(clean_contacto);
  }
  await add_contactos(clean_contactos);
}

export async function list_contacts(api_key) {
  const CONTACTS_API_URL = `https://api.clientify.net/v1/contacts/`;
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  try {
    response = await axios.get(CONTACTS_API_URL, { headers: header });
  } catch (error) {
    console.log(error);
    throw error;
  }

  if (response?.status == 200) {
    return response.data.results;
  } else {
    console.log(response);
    return "";
  }
}

export async function list_pageof_contacts(
  api_key,
  CONTACTS_API_URL = `https://api.clientify.net/v1/contacts/?page_size=250`
) {
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  let flag = false;
  // try 5 times to get a response
  for (let i = 5; i > 0; i--) {
    try {
      response = await axios.get(CONTACTS_API_URL, { headers: header });
      flag = true;
      break;
    } catch (error) {
      console.log(
        `Got response bad response. Will try ${i} more times. Response: `
      );
      console.log(error);
      flag = false;
    }
  }
  if (response?.status == 200) {
    return [response.data.results, response.data.next];
  } else {
    console.log(
      `Got response bad response. No more tries available. Response: ${response.status}`
    );
    throw new Error("Tried too many times");
  }
}

const clean_contactos = async (contactos) => {
  let clean_contactos = [];
  try {
    for (contacto in contactos) {
      const clean_contacto = fix_contacto(contacto);
      clean_contactos.push(clean_contacto);
    }
  } catch (error) {
    throw error;
  }
  return clean_contactos;
};

export async function get_page(api_key) {
  const CONTACTS_API_URL = `https://api.clientify.net/v1/contacts/`;
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let pagina = [];
  await axios
    .get(CONTACTS_API_URL, { headers: header })
    .then(async function (response) {
      if (response.status != 200) {
        console.error(`ERROR contacts_api_url`, response);
      } else {
        pagina = response.data.results;
      }
    });
  return pagina;
}

export async function add_contactos(contactos) {
  let contactos_to_insert = [];
  let contactos_to_update = [];
  let futuros = [];

  contactos.forEach((contacto) => {
    const query = knex_conn("Contactos")
      .select()
      .where("Contacto_id", contacto.id)
      .then(async (rows) => {
        if (rows.length === 0) {
          contactos_to_insert.push(contacto);
        } else {
          contactos_to_update.push(contacto);
        }
      });
    futuros.push(query);
  });

  try {
    await Promise.all(futuros);
  } catch (error) {
    console.log("At least 1 of the previous ~250 contact queries failed.");
    throw error;
  }

  console.log(`contactos_to_insert: ${contactos_to_insert.length}`);
  console.log(`contactos_to_update: ${contactos_to_update.length}`);

  await knex_conn.transaction((trx) => {
    let futuros = [];
    contactos_to_update.forEach((contacto) => {
      console.log(
        `${getISODateTime()} updating : ${contacto.first_name} ${contacto.last_name} -- ${contacto.owner}`
      );
      const query = knex_conn("Contactos")
        .where("Contacto_id", contacto.id)
        .update({
          Propietario: contacto.owner_name,
          Propietario_email: contacto.owner,
          Nombres: contacto.first_name,
          Apellidos: contacto.last_name,
          Cargo: contacto.title,
          Empresa: contacto.company,
          Estado: contacto.status,
          Emails: contacto.emails,
          Telefonos: contacto.phones,
          Origen: contacto.contact_source,
          Tipo: contacto.contact_type,
          Origen_web: contacto.medium,
          Custom_fields: contacto.custom_fields,
          Direcciones: contacto.addresses,
          Creado: contacto.created,
          Modificado: contacto.modified,
          Ultimo_contacto: contacto.last_contact,
          Cedula: contacto.Cedula,
          Tags: contacto.tags ? contacto.tags : "",
          Periodo: contacto.Periodo ? contacto.Periodo : "",
          Carrera: contacto.Carrera ? contacto.Carrera : "",
          Escuela: contacto.Escuela ? contacto.Escuela : "",
          Facultad: contacto.Facultad ? contacto.Facultad : "",
          Grado: contacto.Grado ? contacto.Grado : "",
          AsesorHistorico: contacto.AsesorHistorico
            ? contacto.AsesorHistorico
            : "",
        })
        .transacting(trx); // This makes every update be in the same transaction
      futuros.push(query);
    });

    Promise.all(futuros) // Once every query is written
      .then(trx.commit); // We try to execute all of them
    // .catch(trx.rollback); // And rollback in case any of them goes wrong
  });

  await knex_conn.transaction((trx) => {
    let futuros = [];
    trx;
    contactos_to_insert.forEach((contacto) => {
      console.log(
        `inserting: ${contacto.first_name} ${contacto.last_name} -- ID: ${contacto.id} / Owner ${contacto.owner}`
      );
      const query = knex_conn("Contactos")
        .where("Contacto_id", contacto.id)
        .insert({
          Contacto_id: contacto.id,
          Propietario: contacto.owner_name,
          Propietario_email: contacto.owner,
          Nombres: contacto.first_name,
          Apellidos: contacto.last_name,
          Cargo: contacto.title,
          Empresa: contacto.company,
          Estado: contacto.status,
          Emails: contacto.emails,
          Telefonos: contacto.phones,
          Origen: contacto.contact_source,
          Tipo: contacto.contact_type,
          Origen_web: contacto.medium,
          Custom_fields: contacto.custom_fields,
          Direcciones: contacto.addresses,
          Creado: contacto.created,
          Modificado: contacto.modified,
          Ultimo_contacto: contacto.last_contact,
          Cedula: contacto.Cedula,
          Tags: contacto.tags ? contacto.tags : "",
          Periodo: contacto.Periodo ? contacto.Periodo : "",
          Carrera: contacto.Carrera ? contacto.Carrera : "",
          Escuela: contacto.Escuela ? contacto.Escuela : "",
          Facultad: contacto.Facultad ? contacto.Facultad : "",
          Grado: contacto.Grado ? contacto.Grado : "",
          AsesorHistorico: contacto.AsesorHistorico
            ? contacto.AsesorHistorico
            : "",
        })
        .transacting(trx); // This makes every update be in the same transaction
      futuros.push(query);
    });

    Promise.all(futuros) // Once every query is written
      .then(trx.commit); // We try to execute all of them
    // .catch(trx.rollback); // And rollback in case any of them goes wrong
  });
}
function getISODateTime() {
  const now = new Date();
  return (
    now.toISOString().replace("T", " ").split(".")[0] +
    "." +
    now.getMilliseconds().toString().padStart(3, "0")
  );
}
