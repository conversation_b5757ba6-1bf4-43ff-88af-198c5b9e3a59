// const axios = require("axios");
import axios from "axios";
import { config, q10_api_key } from "./config.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});

const API_URL = "https://api.q10.com/v1/descuentos";
const LIMIT = 400;
let OFFSET = 0;

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

export async function sync_descuentos() {
  try {
    const response = await axios.get(API_URL, {
      params: { Limit: LIMIT, Offset: OFFSET },
      headers: {
        "API-Key": q10_api_key,
      },
    });

    const data = response.data;
    if (data.length === 0) {
      log("No more data to fetch.");
      await knex_conn.destroy();
      return;
    }

    const descuentosData = [];
    for (const descuento of data) {
      const existingdescuento = await knex_conn("descuentos")
        .select("Codigo")
        .where("Codigo", descuento.Codigo)
        .first();
      if (!existingdescuento) {
        descuentosData.push({
          Codigo: descuento.Codigo,
          Nombre: descuento.Nombre,
          Tipo: descuento.Tipo,
          Caracter: descuento.Caracter,
          Valor: descuento.Valor,
        });
      }
    }
    if (descuentosData.length > 0) {
      await knex_conn.transaction(async (trx) => {
        await trx("descuentos").insert(descuentosData);
      });
      log(`Fetched and stored ${data.length} records.`);
    }

    OFFSET += LIMIT;

    if (data.length === LIMIT) {
      sync_descuentos();
    }
  } catch (error) {
    logError("Error fetching data:", error);
    await knex_conn.destroy();
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  (async () => {
    try {
      await sync_descuentos();
    } catch (error) {
      logError("Error creating table or fetching data:", error);
      await knex_conn.destroy();
    }
  })();
}
