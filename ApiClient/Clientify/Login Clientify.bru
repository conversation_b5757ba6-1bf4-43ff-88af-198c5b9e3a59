meta {
  name: Login Clientify
  type: http
  seq: 2
}

post {
  url: https://api.clientify.net/v1/api-auth/obtain_token/
  body: json
  auth: inherit
}

headers {
  ~Content-Type: application/json
}

body:json {
  {
    "username": "{{clientify_usr}}",
    "password": "{{clientify_psw}}"
  }
  
}

script:post-response {
  var jsonData = res.getBody();
  bru.setEnvVar("token", jsonData.token);
}

settings {
  encodeUrl: true
}
