import axios from "axios";
import { config, usr, psw } from "./config.js";
import {
  to_cr_time_string,
  get_token,
  close_connection,
} from "./primitives.js";
import {
  add_page,
  get_contact,
  add_contacts,
  list_pageof_contacts,
} from "./utils_contactos.js";
import { fix_task, add_task } from "./utils_tasks.js";
import { fix_wallentry, add_wallentry } from "./utils_wallentries.js";
import { add_test_wallentry } from "./test_wallentries.js";
import { add_test_task } from "./test_tasks.js";

export async function add_task_timeline(token, contact_id) {
  const full_contact = await get_contact(token, contact_id);

  // console.log(full_contact);

  for (const i in full_contact.related_tasks) {
    const task = full_contact.related_tasks[i];
    let clean_task = undefined;
    try {
      clean_task = fix_task(task);
    } catch (error) {
      console.log(
        `Could not fix task from ${contact_id}: ${JSON.stringify(task)}`
      );
      throw error;
    }

    try {
      await add_task(clean_task, contact_id);
    } catch (error) {
      console.log(
        `Could not add task from ${contact_id}: ${JSON.stringify(task)}`
      );
      throw error;
    }
  }
}

export async function add_task_timeline_all(token) {
  let next_page = undefined;
  let contacts = undefined;
  let futuros = [];
  let added_contacto = undefined;
  while (true) {
    [contacts, next_page] = await list_pageof_contacts(token, next_page);
    futuros = [];
    for (const contacto of contacts) {
      console.log(
        `${contacto.first_name} ${contacto.last_name} -- ${contacto.id}`
      );
      try {
        added_contacto = add_task_timeline(token, contacto.id);
      } catch (error) {
        console.log(
          `Could not queue contact: ${contacto.first_name} ${contacto.last_name} -- ${contacto.id}`
        );
        continue;
      }
      futuros.push(added_contacto);
    }

    let results = undefined;
    try {
      // Block until all contacts are done.
      results = await Promise.allSettled(futuros);
    } catch (error) {
      console.log("At least 1 of the previous ~25 contacts failed to sync.");
      throw error;
    }

    // Record the ones that failed.
    const failed_contacts = results.filter((p) => p.status === "rejected");
    for (const i in failed_contacts) {
      const failed_contact = failed_contacts[i];
      console.log(`FAILED CONTACT URL: ${failed_contact?.reason?.config?.url}`);
    }

    console.log(next_page);
    if (next_page == null) {
      break;
    }
  }
}

export async function add_task_timeline_contact_ids(token, contact_ids) {
  for (const id of contact_ids) {
    console.log(`${id}`);
    await add_task_timeline(token, id);
  }
}

export async function sync_tasks_timeline(usr, psw) {
  let token = await get_token(usr, psw);
  await add_task_timeline_all(token);
  console.log(" --- done syncing tasks from timeline--- ");
  return token;
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  const token = await sync_tasks_timeline(usr, psw);
  await close_connection(token);
  process.exit();
}
