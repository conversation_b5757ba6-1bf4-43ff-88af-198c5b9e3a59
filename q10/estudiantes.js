import axios from "axios";
import { config, q10_api_key } from "./config.js";
import knex from "knex";
import { log, logError } from "../utils/logging.js";
import async from "async";

const knex_conn = knex({
  client: "mssql",
  connection: config,
});
// API URL
const ESTUDIANTES_API_URL = "https://api.q10.com/v1/estudiantes";
const PROGRAMAS_API_URL = "https://api.q10.com/v1/programas";
const PERIODOS_API_URL = "https://api.q10.com/v1/periodos";
const SEDE_JORNADA_API_URL = "https://api.q10.com/v1/sedesjornadas";

const LIMIT = 900;
let OFFSET = 0;
let moreData = true;

export async function sync_estudiante(estudiante) {
  log(`Codigo_estudiante: ${estudiante.Codigo_estudiante}`);
  await knex_conn("Estudiante")
    .select()
    .where("Codigo_estudiante", estudiante.Codigo_estudiante)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Estudiante").insert({
          Codigo_estudiante: estudiante.Codigo_estudiante,
          Primer_nombre: estudiante.Primer_nombre,
          Segundo_nombre: estudiante.Segundo_nombre,
          Primer_apellido: estudiante.Primer_apellido,
          Segundo_apellido: estudiante.Segundo_apellido,
          Numero_identificacion: estudiante.Numero_identificacion,
          Email: estudiante.Email,
          Direccion: estudiante.Direccion,
          Telefono: estudiante.Telefono,
          Celular: estudiante.Celular,
          Direccion: estudiante.Direccion,
          Genero: estudiante?.Genero,
        });
      } else {
        await knex_conn("Estudiante")
          .update({
            Primer_nombre: estudiante.Primer_nombre,
            Segundo_nombre: estudiante.Segundo_nombre,
            Primer_apellido: estudiante.Primer_apellido,
            Segundo_apellido: estudiante.Segundo_apellido,
            Numero_identificacion: estudiante.Numero_identificacion,
            Email: estudiante.Email,
            Telefono: estudiante.Telefono,
            Celular: estudiante.Celular,
            Direccion: estudiante.Direccion,
            Genero: estudiante?.Genero,
          })
          .where("Codigo_estudiante", estudiante.Codigo_estudiante);
      }
    });
}

export async function list_programas() {
  let programas = [];
  try {
    let response = undefined;

    while (true) {
      try {
        // Fetch all courses

        response = await axios.get(PROGRAMAS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `(Programas) Total number of pages: ${pages}, Total Items to get ${n_items}, current page number: ${actualPage}, current offset: ${OFFSET}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      programas.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  }

  return programas;
}

export async function list_periodos() {
  OFFSET = 1;
  let periodos = [];
  try {
    let response = undefined;
    while (true) {
      try {
        // Fetch all courses
        response = await axios.get(PERIODOS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `(periodos) Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}, current offset: ${OFFSET}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      periodos.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  }

  return periodos;
}

export async function list_sede_jornadas() {
  let sede_jornadas = [];
  try {
    let response = undefined;

    while (true) {
      try {
        // Fetch all courses

        response = await axios.get(SEDE_JORNADA_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `(list_sede_jornadas) Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}, current offset: ${OFFSET}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      sede_jornadas.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  }

  return sede_jornadas;
}

export async function sync_estudiantes() {
  // console.log("Get programas");
  // const programas = await list_programas();
  log("Get periodos");
  const periodos = await list_periodos();
  // console.log("Get sede_jornadas");
  // const sede_jornadas = await list_sede_jornadas();

  await sync_estudiantes_periodo(periodos);
}

async function sync_estudiantes_sj_p_p(sede_jornadas, periodos, programas) {
  for (const j in periodos) {
    log(`Periodo: ${periodos[j].Consecutivo}`);
    for (const k in programas) {
      log(`Programa: ${programas[k].Codigo}`);
      async.mapLimit(
        sede_jornadas,
        10,
        async (sede_jornada_item) => {
          const estudiantes = await get_estudiantes(
            periodos[j].Consecutivo,
            sede_jornada_item.Consecutivo,
            programas[k].Codigo,
            true
          );

          // Use async.mapLimit to limit concurrency to 3 sync_estudiante operations at a time
          await new Promise((resolve) => {
            async.mapLimit(
              estudiantes,
              3, // Maximum 3 concurrent operations
              async (estudiante) => {
                try {
                  await sync_estudiante(estudiante);
                  return true;
                } catch (error) {
                  logError(`FAILED ESTUDIANTE: ${error}`);
                  return false; // Return false instead of throwing to continue processing other students
                }
              },
              (err, results) => {
                if (err) {
                  logError(`Error in sync_estudiantes_sj_p_p: ${err}`);
                }
                resolve(results);
              }
            );
          });

          return true; // <- return a value!
        },
        (err, contents) => {
          if (err) throw err;
          log(contents);
        }
      );
    }
  }
}

async function sync_estudiantes_periodo(periodos) {
  for (const i in periodos) {
    const periodo_id = periodos[i].Consecutivo;
    const estudiantes = await get_estudiantes_periodo(periodo_id);

    log(`Estudiantes de periodo ${periodo_id}: ${estudiantes.length}`);

    // Use async.mapLimit to limit concurrency to 3 sync_estudiante operations at a time
    await new Promise((resolve) => {
      async.mapLimit(
        estudiantes,
        3, // Maximum 3 concurrent operations
        async (estudiante) => {
          try {
            await sync_estudiante(estudiante);
            return true;
          } catch (error) {
            logError(`FAILED ESTUDIANTE: ${error}`);
            return false; // Return false instead of throwing to continue processing other students
          }
        },
        (err, results) => {
          if (err) {
            logError(`Error in sync_estudiantes_periodo: ${err}`);
          }
          resolve(results);
        }
      );
    });
  }
}

export async function get_estudiantes_periodo(periodo) {
  let estudiantes = [];
  OFFSET = 1;
  const req_link =
    ESTUDIANTES_API_URL +
    `?Periodo=${periodo}` +
    `&Limit=${LIMIT}` ;

  log(req_link);
  // const instance = axios.create({
  //   baseURL: req_link,
  //   timeout: 30000,
  //   headers: { "API-Key": q10_api_key },
  // });

  try {
    let response = undefined;
    while (true) {
      try {
        response = await axios.get(req_link, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error.code);
        break;
      }
      if (response?.status != 200) {
        break;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-pagesize"];
      const total_items = response.headers["x-paging-totalitemcount"];
      if (pages == 0) {
        //No data available for this period
        log(
          `(get_estudiantes_periodo) No data available for this period. ${periodo}`
        );
        break;
      } else {
        log(
          `(get_estudiantes_periodo) Total number of pages: ${pages}, items on this page ${n_items} of a grand total of ${total_items}, current page number: ${actualPage}, current offset: ${OFFSET}`
        );
      }
      estudiantes.push(...structuredClone(response.data));

      // This API always says it has 0 items, so I'll be breaking after the frist page:
      if (n_items == 0 || pages == actualPage) {
        break;
      } else {
        OFFSET += 1;
      }
    }
  } catch (error) {
    log(error);
  }

  return estudiantes;
}

export async function get_estudiante(Codigo_estudiante) {
  const req_link = ESTUDIANTES_API_URL + "/" + Codigo_estudiante;
  log(`Getting Estudiante ${req_link}`);
  const instance = axios.create({
    baseURL: req_link,
    timeout: 30000,
    headers: { "API-Key": q10_api_key },
  });
  try {
    let response = undefined;
    while (true) {
      try {
        response = await instance.get(req_link, {});
      } catch (error) {
        log(error.code);
        break;
      }
      if (response?.status != 200) {
        break;
      }
      return response.data;
    }
  } catch (error) {
    log(error);
    return false;
  }
}

export async function get_estudiantes(periodo, sede_jornada, programa) {
  const fecha = new Date();
  const today = fecha.toISOString().split("T")[0];

  let estudiantes = [];
  OFFSET = 1;
  const req_link =
    ESTUDIANTES_API_URL +
    `?Fecha_inicio=1986-01-01` +
    `&Fecha_fin=${today}` +
    `&Programa=${programa}` +
    `&Sede_jornada=${sede_jornada}` +
    `&Periodo=${periodo}` +
    `&Limit=${LIMIT}` ;

  log(req_link);
  const instance = axios.create({
    baseURL: req_link,
    timeout: 30000,
    headers: { "API-Key": q10_api_key },
  });

  try {
    let response = undefined;
    while (true) {
      try {
        response = await instance.get(req_link, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
        });
      } catch (error) {
        log(error.code);
        break;
      }
      if (response?.status != 200) {
        break;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      if (pages == "0") {
        `(get_estudiantes) No results for ${req_link} `
        break;
      }
      log(
        `(get_estudiantes) Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}, current offset: ${OFFSET}`
      );
      estudiantes.push(...structuredClone(response.data));

      // This API always says it has 0 items, so I'll be breaking after the frist page:
      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }

      if (pages == actualPage) {
        break;
      } else {
        OFFSET += 1;
      }
    }
  } catch (error) {
    log(error);
  }
  return estudiantes;
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  // Call the function to start fetching and storing data
  await sync_estudiantes();
  process.exit();
}
