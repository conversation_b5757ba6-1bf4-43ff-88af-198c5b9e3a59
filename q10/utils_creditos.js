import axios from "axios";
import { config, q10_api_key } from "./config.js";

// API URL
const ORDENES_DE_PAGO_API_URL = "https://api.q10.com/v1/ordenespago";
const LIMIT = 800;
let OFFSET = 0;
let moreData = true;

export async function get_ordenes_de_pago_from_person(Codigo_estudiante) {
  let response = undefined;
  try {
    // Fetch data from the API for each period
    const newDate = new Date();
    newDate.setFullYear(newDate.getFullYear() - 1);
    const initialDateIso = newDate.toISOString().split("T")[0];
    const today = new Date();
    const endDateIso = today.toISOString().split("T")[0];

    response = await axios.get(ORDENES_DE_PAGO_API_URL, {
      params: {
        Limit: LIMIT,
        Offset: OFFSET,
        Codigo_persona: <PERSON><PERSON>_estudiante,
        Fecha_inicio: initialDateIso,
        Fecha_fin: endDateIso,
      },
      headers: {
        "API-Key": q10_api_key,
      },
    });
  } catch (error) {
    console.log(`Failed link: ${ORDENES_DE_PAGO_API_URL}`);
    console.log(error);
  }
  if (response?.status != 200) {
    console.log(`Did not get a 200: ${ORDENES_DE_PAGO_API_URL}`);
  }
  const pages = response.headers["x-paging-pagecount"];
  const totalResults = response.headers["x-paging-totalitemcount"];
  const actualPage = response.headers["x-paging-x-paging-pagenumber"];
  if (pages > actualPage) {
    console.log(
      `ERROR, more than 1 page, actualPage: ${actualPage}, pages: ${pages}`
    );
  }

  return response.data;
}

export async function add_orden_de_pago(
  trx,
  orden_de_pago,
  Consecutivo_credito
) {
  const existingOrdPago = await trx("Ordenes_pago")
    .select("Numero_orden_pago")
    .where("Numero_orden_pago", orden_de_pago.Numero_orden_pago)
    .first();
  if (!existingOrdPago) {
    await trx("Ordenes_pago").insert({
      Numero_orden_pago: orden_de_pago.Numero_orden_pago,
      Consecutivo_credito: Consecutivo_credito,
      Fecha_orden_pago: orden_de_pago.Fecha_orden_pago,
      Valor_total: orden_de_pago.Valor_total,
      Valor_en_este_credito: orden_de_pago.Valor_en_este_credito,
      Consecutivo_Periodo: orden_de_pago.Consecutivo_Periodo,
      Nombre_periodo: orden_de_pago.Nombre_periodo,
      Estado_orden_pago: orden_de_pago.Estado_orden_pago,
      Valor_pagado: orden_de_pago.Valor_pagado,
      Justificacion_anulacion: orden_de_pago.Justificacion_anulacion,
      Anulada: orden_de_pago.Anulada,
      Observaciones_orden_pago: orden_de_pago.Observaciones_orden_pago,
      Numero_identificacion_cajero: orden_de_pago.Numero_identificacion_cajero,
      Fecha_vencimiento_1: orden_de_pago.Fecha_vencimiento_1,
      Codigo_persona: orden_de_pago.Codigo_persona,
      Fecha_anulacion: orden_de_pago.Fecha_anulacion,
      Numero_identificacion: orden_de_pago.Numero_identificacion,
      Orden_pago_paz_y_salvo: orden_de_pago.Orden_pago_paz_y_salvo,
      Valor_pendiente: orden_de_pago.Valor_pendiente,
      Nombre_cajero: orden_de_pago.Nombre_cajero,
      Codigo_cajero: orden_de_pago.Codigo_cajero,
    });
    // console.log(
    //   `Credito #${Consecutivo_credito} - Orden de Pago #${orden_de_pago.Numero_orden_pago} - Ingresado`
    // );
  } else {
    await trx("Ordenes_pago")
      .where("Numero_orden_pago", orden_de_pago.Numero_orden_pago)
      .update({
        Consecutivo_credito: Consecutivo_credito,
        Fecha_orden_pago: orden_de_pago.Fecha_orden_pago,
        Valor_total: orden_de_pago.Valor_total,
        Valor_en_este_credito: orden_de_pago.Valor_en_este_credito,
        Consecutivo_Periodo: orden_de_pago.Consecutivo_periodo,
        Nombre_periodo: orden_de_pago.Nombre_periodo,
        Estado_orden_pago: orden_de_pago.Estado_orden_pago,
        Valor_pagado: orden_de_pago.Valor_pagado,
        Justificacion_anulacion: orden_de_pago.Justificacion_anulacion,
        Anulada: orden_de_pago.Anulada,
        Observaciones_orden_pago: orden_de_pago.Observaciones_orden_pago,
        Numero_identificacion_cajero:
          orden_de_pago.Numero_identificacion_cajero,
        Fecha_vencimiento_1: orden_de_pago.Fecha_vencimiento_1,
        Codigo_persona: orden_de_pago.Codigo_persona,
        Fecha_anulacion: orden_de_pago.Fecha_anulacion,
        Numero_identificacion: orden_de_pago.Numero_identificacion,
        Orden_pago_paz_y_salvo: orden_de_pago.Orden_pago_paz_y_salvo,
        Valor_pendiente: orden_de_pago.Valor_pendiente,
        Nombre_cajero: orden_de_pago.Nombre_cajero,
        Codigo_cajero: orden_de_pago.Codigo_cajero,
      });
    // console.log(
    //   `Credito #${Consecutivo_credito} - Orden de Pago #${orden_de_pago.Numero_orden_pago} - Actualizado`
    // );
  }
}

export async function add_credito_ordenDePago_cuota(
  trx,
  credito,
  ordenes_pagos,
  cuotas,
  existingCredito
) {
  for (const cuota of cuotas) {
    await add_cuota(trx, cuota, credito.Consecutivo_credito);
  }

  for (const ordenPago of ordenes_pagos) {
    await add_orden_de_pago(trx, ordenPago, credito.Consecutivo_credito);
  }

  if (!existingCredito) {
    console.log(`Adding credito: ${credito.Consecutivo_credito}`);
    await insert_credito(trx, credito);
  } else {
    console.log(`Updating credito: ${credito.Consecutivo_credito}`);
    await update_credito(trx, credito);
  }
}

export async function insert_credito(trx, credit) {
  try {
    await trx("creditos").insert(credit);
  } catch (error) {
    console.log(credit);

    throw error;
  }
}
export async function update_credito(trx, credit) {
  // updateObject.Estado_credito = existingCreditos.Estado_credito;

  await trx("creditos")
    .where("Consecutivo_credito", credit.Consecutivo_credito)
    .update({
      Codigo_estudiante: credit.Codigo_estudiante,
      Nombre_completo: credit.Nombre_completo,
      Numero_identificacion: credit.Numero_identificacion,
      Pagare: credit.Pagare,
      Fecha: credit.Fecha,
      Consecutivo_periodo: credit.Consecutivo_periodo,
      Nombre_periodo: credit.Nombre_periodo,
      Pago_anticipado: credit.Pago_anticipado,
      Fecha_primera_cuota: credit.Fecha_primera_cuota,
      Periodicidad_cuotas: credit.Periodicidad_cuotas,
      Numero_cuotas: credit.Numero_cuotas,
      Interes_corriente: credit.Interes_corriente,
      Dias_gracia: credit.Dias_gracia,
      Interes_mora: credit.Interes_mora,
      Penalizacion_mora: credit.Penalizacion_mora,
      Codigo_programa: credit.Codigo_programa,
      Nombre_programa: credit.Nombre_programa,
      Codigo_sede: credit.Codigo_sede,
      Nombre_sede: credit.Nombre_sede,
      Codigo_jornada: credit.Codigo_jornada,
      Nombre_jornada: credit.Nombre_jornada,
      Observaciones: credit.Observaciones,
      Codigo_codeudor: credit.Codigo_codeudor,
      Nombre_completo_codeudor: credit.Nombre_completo_codeudor,
      Numero_identificacion_codeudor: credit.Numero_identificacion_codeudor,
      Consecutivo_tercero: credit.Consecutivo_tercero,
      Nombre_completo_tercero: credit.Nombre_completo_tercero,
      Numero_identificacion_tercero: credit.Numero_identificacion_tercero,
      Valor_credito: credit.Valor_credito,
      Valor_capital_pendiente: credit.Valor_capital_pendiente,
      Valor_interes_corriente: credit.Valor_interes_corriente,
      Valor_interes_mora: credit.Valor_interes_mora,
      Valor_penalizacion: credit.Valor_penalizacion,
      Total_pendiente: credit.Total_pendiente,
      Pago_minimo: credit.Pago_minimo,
      Tipo_penalizacion: credit.Tipo_penalizacion,
      Periodicidad_penalizacion: credit.Periodicidad_penalizacion,
      Responsable_credito: credit.Responsable_credito,
      Estado_credito: credit.Estado_credito,
      Dias_entre_cuotas: credit.Dias_entre_cuotas,
      Codigo_cajero: credit.Codigo_cajero,
      Numero_identificacion_cajero: credit.Numero_identificacion_cajero,
      Nombre_completo_cajero: credit.Nombre_completo_cajero,
      created_at: credit.created_at,
    });
}

export function is_cuota_modified(cuota, existingCuota) {
  if (
    cuota.Fecha_cuota.split("T")[0] !=
    existingCuota.Fecha_cuota.toISOString().split("T")[0]
  ) {
    return true;
  }
  if (cuota.Capital != existingCuota.Capital) {
    return true;
  }
  if (parseFloat(cuota.Interes_corriente) != existingCuota.Interes_corriente) {
    //   console.log("Valor_interes_corriente", cuota, existingCuota);
    return true;
  }
  if (parseFloat(cuota.Interes_mora) != existingCuota.Interes_mora) {
    return true;
    //   console.log("mora", cuota, existingCuota);
  }
  if (parseFloat(cuota.Penalizacion) != existingCuota.Penalizacion) {
    //   console.log("pena", cuota, existingCuota);
    return true;
  }
  if (parseFloat(cuota.Total_cuota_hoy) != existingCuota.Total_cuota_hoy) {
    //   console.log("total", cuota, existingCuota);
    return true;
  }
  if (parseFloat(cuota.Pagado) != existingCuota.Pagado) {
    //   console.log("pagado", cuota, existingCuota);
    return true;
  }
  if (parseFloat(cuota.Pendiente) != existingCuota.Pendiente) {
    //   console.log("pend", cuota, existingCuota);
    return true;
  }
  return false;
}

export async function add_cuota(trx, cuota, Consecutivo_credito) {
  const existingCuota = await trx("Cuotas")
    .select(
      "Consecutivo_credito",
      "Fecha_cuota",
      "Capital",
      "Interes_corriente",
      "Interes_mora",
      "Penalizacion",
      "Total_cuota_hoy",
      "Pagado",
      "Pendiente"
    )
    .where("Consecutivo_credito", Consecutivo_credito)
    .where("Numero_cuota", cuota.Numero_cuota)
    .first();
  if (!existingCuota) {
    await trx("cuotas").insert({
      ...cuota,
      Consecutivo_credito: Consecutivo_credito,
    });
    // console.log(
    //   `Credito #${Consecutivo_credito} - Cuota #${cuota.Numero_cuota} - Ingresada`
    // );
  } else {
    const update = is_cuota_modified(cuota, existingCuota);

    if (update) {
      await trx("cuotas")
        .update({
          ...cuota,
        })
        .where("Consecutivo_credito", Consecutivo_credito)
        .where("Numero_cuota", cuota.Numero_cuota);
      // console.log(
      //   `Credito #${Consecutivo_credito} Cuota ${cuota.Numero_cuota} actualizada`
      // );
    } else {
      // console.log(
      //   `Credito #${Consecutivo_credito} Cuota ${cuota.Numero_cuota} no ha cambiado.`
      // );
    }
  }
}
