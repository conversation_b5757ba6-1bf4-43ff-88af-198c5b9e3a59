import axios from "axios";
import { config, q10_api_key } from "./config.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});
// graph TD

// Creditos -->  Periodos
// Creditos -->|Consulta \n por estados| Creditos["/creditos"]
// Creditos --> Cuotas
// Creditos --> Ordenes_Pago

// Ordenes_de_Pago["/ordenespago"] -->|Consulta\n por Fechas| Ordenes_Pago

// Pagos["/pagos/creditos"] --> Forma_Pago[Formas\n de pago]
// Pagos --> Detalle_Cuotas[Detalle \n de cuotas]

// API URL
const CREDITOS_API_URL = "https://api.q10.com/v1/creditos";
const ORDENES_DE_PAGO_API_URL = "https://api.q10.com/v1/ordenespago";
const LIMIT = 800;
let OFFSET = 0;
let moreData = true;
const estadosCreditos = ["Al día", "Mora", "Paz y salvo", "Anulados"];

export async function get_all_ordenes_de_pago(Consecutivo_credito) {
  try {
    // Fetch data from the API for each period
    const newDate = new Date();
    newDate.setFullYear(newDate.getFullYear() - 1);
    const initialDateIso = newDate.toISOString().split("T")[0];
    const today = new Date();
    const endDateIso = today.toISOString().split("T")[0];

    response = await axios.get(ORDENES_DE_PAGO_API_URL, {
      params: {
        Limit: LIMIT,
        Offset: OFFSET,
        Codigo_persona: Consecutivo_credito,
        Fecha_inicio: initialDateIso,
        Fecha_fin: endDateIso,
      },
      headers: {
        "API-Key": q10_api_key,
      },
    });
  } catch (error) {
    console.log(`Failed link: ${ORDENES_DE_PAGO_API_URL}`);
    console.log(error);
  }
  if (response?.status != 200) {
    console.log(`Got a 200: ${ORDENES_DE_PAGO_API_URL}`);
    console.log(error);
  }
  const pages = response.headers["x-paging-pagecount"];
  const totalResults = response.headers["x-paging-totalitemcount"];
  const actualPage = response.headers["x-paging-x-paging-pagenumber"];
  if (pages > actualPage) {
    console.log(
      `ERROR, more than 1 page, actualPage: ${actualPage}, pages: ${pages}`
    );
  }

  return response.data;
}

export async function sync_ordenes_pago(ordenesPago, Consecutivo_credito) {
  const all_pagos = await get_all_ordenes_de_pago(Consecutivo_credito);

  console.log(all_pagos);
  return;

  // Insert into ordenes_pago related table
  for (const ordenPago of ordenesPago) {
    const existingOrdPago = await trx("Ordenes_pago")
      .select("Numero_orden_pago")
      .where("Numero_orden_pago", ordenPago.Numero_orden_pago)
      .first();
    if (!existingOrdPago) {
      await trx("ordenes_pago").insert({
        ...ordenPago,
        Consecutivo_credito: Consecutivo_credito,
      });
    }
  }
}

export async function sync_creditos() {
  try {
    const periods = await knex_conn("periods").select("Consecutivo");
    // const periods = [1]
    for (const period of periods) {
      // Fetch data from the API for each period
      let response = undefined;
      // if (period.Consecutivo != "1079") {
      //     //This is for only doing certain period
      //     continue;
      // }
      for (const estadoCredito of estadosCreditos) {
        moreData = true;
        while (moreData) {
          //Get that at least once, if more data is avail, do it again
          moreData = false;
          try {
            response = await axios.get(CREDITOS_API_URL, {
              params: {
                Limit: LIMIT,
                Offset: OFFSET,
                Consecutivo_periodo: period.Consecutivo,
                Estado_credito: estadoCredito,
              },
              headers: {
                "API-Key": q10_api_key,
              },
            });
          } catch (error) {
            console.log(
              `Periodo #${period.Consecutivo} en estado ${estadoCredito} no tiene creditos: `
            );
            continue;
          }
          if (response?.status != 200) {
            continue;
          }
          const pages = response.headers["x-paging-pagecount"];
          const totalResults = response.headers["x-paging-totalitemcount"];
          const actualPage = response.headers["x-paging-x-paging-pagenumber"];
          if (pages > actualPage && pages != 0) {
            moreData = true;
          }
          const data = response.data;
          console.log(
            `Periodo: #${period.Consecutivo} - con ${totalResults} créditos en estado ${estadoCredito} - Procesando los primeros ${LIMIT}`
          );
          // console.log(JSON.stringify(data));
          // Insert data into the database
          for (const credit of data) {
            if (!credit.Codigo_programa) {
              continue;
            }
            // if (!credit.Consecutivo_credito != "17413") {
            //     continue;
            // }
            // console.log(estadoCredito, credit)
            await knex_conn.transaction(async (trx) => {
              const existingCreditos = await trx("creditos")
                .select([
                  "Estado_credito",
                  "Valor_capital_pendiente",
                  "Valor_credito",
                  "Valor_interes_corriente",
                  "Valor_interes_mora",
                  "Valor_penalizacion",
                  "Total_pendiente",
                  "Fecha",
                ])
                .where("Consecutivo_credito", credit.Consecutivo_credito)
                .first();

              if (!existingCreditos) {
                await knex_conn.transaction(async (trx) => {
                  // Insert into the main credito table
                  const cuotas = credit.Cuotas;
                  delete credit.Cuotas;
                  const ordenesPago = credit.Ordenes_pago;
                  delete credit.Ordenes_pago;
                  credit.Estado_credito = estadoCredito;
                  await trx("creditos").insert(credit);

                  // Insert into cuotas related table
                  for (const cuota of cuotas) {
                    const existingCuota = await trx("Cuotas")
                      .select("Consecutivo_credito")
                      .where("Consecutivo_credito", credit.Consecutivo_credito)
                      .where("Numero_cuota", cuota.Numero_cuota)
                      .first();
                    if (!existingCuota) {
                      await trx("cuotas").insert({
                        ...cuota,
                        Consecutivo_credito: credit.Consecutivo_credito,
                      });
                    } else {
                      await trx("cuotas")
                        .update({
                          ...cuota,
                        })
                        .where(
                          "Consecutivo_credito",
                          credit.Consecutivo_credito
                        )
                        .where("Numero_cuota", cuota.Numero_cuota);
                    }
                  }

                  await sync_ordenes_pago(
                    ordenesPago,
                    credit.Consecutivo_credito
                  );
                  trx.commit;
                });
              } else {
                const updateObject = {};
                // console.log(credit.Fecha.split("T")[0])
                // console.log(existingCreditos.Fecha.toISOString().split("T")[0])
                // process.exit()
                if (
                  credit.Fecha.split("T")[0] !=
                  existingCreditos.Fecha.toISOString().split("T")[0]
                ) {
                  updateObject.Fecha = credit.Fecha;
                }

                if (estadoCredito != existingCreditos.Estado_credito) {
                  updateObject.Estado_credito = estadoCredito;
                  console.log(
                    "BUSCANDO ",
                    estadoCredito,
                    " PERO TENIA ",
                    existingCreditos.Estado_credito
                  );
                }
                if (
                  credit.Valor_capital_pendiente !=
                  existingCreditos.Valor_capital_pendiente
                ) {
                  updateObject.Valor_capital_pendiente =
                    credit.Valor_capital_pendiente;
                }
                if (credit.Valor_credito != existingCreditos.Valor_credito) {
                  updateObject.Valor_credito = credit.Valor_credito;
                }
                if (
                  credit.Valor_interes_corriente !=
                  existingCreditos.Valor_interes_corriente
                ) {
                  updateObject.Valor_interes_corriente =
                    credit.Valor_interes_corriente;
                }
                if (
                  credit.Valor_interes_mora !=
                  existingCreditos.Valor_interes_mora
                ) {
                  updateObject.Valor_interes_mora = credit.Valor_interes_mora;
                }
                if (
                  credit.Valor_penalizacion !=
                  existingCreditos.Valor_penalizacion
                ) {
                  updateObject.Valor_penalizacion = credit.Valor_penalizacion;
                }
                if (
                  credit.Total_pendiente != existingCreditos.Total_pendiente
                ) {
                  updateObject.Total_pendiente = credit.Total_pendiente;
                }
                if (Object.entries(updateObject).length > 0) {
                  const existingCreditos = await knex_conn("creditos")
                    .where("Consecutivo_credito", credit.Consecutivo_credito)
                    .where("Consecutivo_periodo", credit.Consecutivo_periodo)
                    .update(updateObject);
                  console.log(
                    `Credito #${credit.Consecutivo_credito} Actualizado`,
                    JSON.stringify(updateObject)
                  );
                  await trx.commit;
                }
                const cuotas = credit.Cuotas;
                delete credit.Cuotas;
                const ordenesPago = credit.Ordenes_pago;
                delete credit.Ordenes_pago;

                // Insert into cuotas related table
                for (const cuota of cuotas) {
                  const existingCuota = await trx("Cuotas")
                    .select(
                      "Consecutivo_credito",
                      "Fecha_cuota",
                      "Capital",
                      "Interes_corriente",
                      "Interes_mora",
                      "Penalizacion",
                      "Total_cuota_hoy",
                      "Pagado",
                      "Pendiente"
                    )
                    .where("Consecutivo_credito", credit.Consecutivo_credito)
                    .where("Numero_cuota", cuota.Numero_cuota)
                    .first();
                  if (!existingCuota) {
                    await trx("cuotas").insert({
                      ...cuota,
                      Consecutivo_credito: credit.Consecutivo_credito,
                    });
                  } else {
                    let update = false;
                    if (
                      cuota.Fecha_cuota.split("T")[0] !=
                      existingCuota.Fecha_cuota.toISOString().split("T")[0]
                    ) {
                      update = true;
                    }
                    if (cuota.Capital != existingCuota.Capital) {
                      update = true;
                    }
                    if (
                      parseFloat(cuota.Interes_corriente) !=
                      existingCuota.Interes_corriente
                    ) {
                      console.log(
                        "Valor_interes_corriente",
                        cuota,
                        existingCuota
                      );
                      update = true;
                    }
                    if (
                      parseFloat(cuota.Interes_mora) !=
                      existingCuota.Interes_mora
                    ) {
                      update = true;
                      console.log("mora", cuota, existingCuota);
                    }
                    if (
                      parseFloat(cuota.Penalizacion) !=
                      existingCuota.Penalizacion
                    ) {
                      console.log("pena", cuota, existingCuota);
                      update = true;
                    }
                    if (
                      parseFloat(cuota.Total_cuota_hoy) !=
                      existingCuota.Total_cuota_hoy
                    ) {
                      console.log("total", cuota, existingCuota);
                      update = true;
                    }
                    if (parseFloat(cuota.Pagado) != existingCuota.Pagado) {
                      console.log("pagado", cuota, existingCuota);
                      update = true;
                    }
                    if (
                      parseFloat(cuota.Pendiente) != existingCuota.Pendiente
                    ) {
                      console.log("pend", cuota, existingCuota);
                      update = true;
                    }

                    if (update) {
                      await trx("cuotas")
                        .update({
                          ...cuota,
                        })
                        .where(
                          "Consecutivo_credito",
                          credit.Consecutivo_credito
                        )
                        .where("Numero_cuota", cuota.Numero_cuota);
                      console.log(
                        `Credito #${credit.Consecutivo_credito} Cuota ${cuota.Numero_cuota} actualizada`
                      );
                    }
                  }
                }

                // Insert into ordenes_pago related table
                for (const ordenPago of ordenesPago) {
                  const existingOrdPago = await knex_conn("Ordenes_pago")
                    .select(["Valor_total"])
                    .where("Numero_orden_pago", ordenPago.Numero_orden_pago)
                    .first();
                  if (!existingOrdPago) {
                    await trx("ordenes_pago").insert({
                      ...ordenPago,
                      Consecutivo_credito: credit.Consecutivo_credito,
                    });
                  } else {
                    const updateObject = {};
                    if (existingOrdPago.Valor_total != ordenPago.Valor_total) {
                      updateObject.Valor_total = ordenPago.Valor_total;
                    }
                    if (Object.entries(updateObject).length > 0) {
                      console.log(existingOrdPago, ordenPago);
                      const existingCreditos = await knex_conn("Ordenes_pago")
                        .where("Numero_orden_pago", ordenPago.Numero_orden_pago)
                        .update(updateObject);
                      console.log(
                        `Credito #${credit.Consecutivo_credito} - Orden de Pago #${ordenPago.Numero_orden_pago}- Actualizado`
                      );
                    }
                  }
                }
                trx.commit;
              }
            });
            // process.exit();
          }
        }
      }
    }

    console.log("Data stored in the database.");
  } catch (error) {
    console.error("Error:", error);
  } finally {
    // Destroy the connection pool
    await knex_conn.destroy();
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  // Call the function to start fetching and storing data
  await sync_creditos();
  process.exit();
}
