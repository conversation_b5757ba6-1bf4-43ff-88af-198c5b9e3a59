import axios from "axios";
import { config, usr, psw } from "./config.js";
import {
  to_cr_time_string,
  get_token,
  close_connection,
} from "./primitives.js";
import {
  add_page,
  get_contact,
  add_contacts,
  list_contacts,
} from "./utils_contactos.js";

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}


export async function sync_contactos(usr, psw) {
  let token;
  try {
    token = await get_token(usr, psw);
    await add_contacts(token);
    log(" --- done syncing contactos --- ");
  } catch (error) {
    logError("Error syncing contactos:", error);
  }
  return token;
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  const token = await sync_contactos(usr, psw);
  try {
    await close_connection(token);
  } catch (error) {
    logError("Error closing connection:", error);
  }
  process.exit();
}
