import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_date_string } from "./primitives.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const CONDICION_MATRICULA_API_URL =
  "https://api.q10.com/v1/condicionesMatricula";
const SEDE_JORNADA_API_URL = "https://api.q10.com/v1/sedesjornadas";

const LIMIT = 800;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

async function sync_condicion_matricula(condicion_matricula) {
  log(condicion_matricula.Nombre);
  await knex_conn("Condicion_matricula")
    .select()
    .where("Codigo", condicion_matricula.Codigo)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Condicion_matricula").insert({
          Codigo: condicion_matricula.Codigo,
          Nombre: condicion_matricula.Nombre,
          Estado: condicion_matricula.Estado,
        });
      } else {
        await knex_conn("Condicion_matricula")
          .update({
            Codigo: condicion_matricula.Codigo,
            Nombre: condicion_matricula.Nombre,
            Estado: condicion_matricula.Estado,
          })
          .where("Codigo", condicion_matricula.Codigo);
      }
    });
}

export async function get_condicion_matriculas(estado) {
  let condicion_matriculas = [];
  OFFSET = 1;
  let pages = undefined;
  let actualPage = undefined;
  let n_items = undefined;
  const req_link = CONDICION_MATRICULA_API_URL + `?Estado=${estado}`;

  log(req_link);
  const instance = axios.create({
    baseURL: req_link,
    timeout: 50000,
    headers: { "API-Key": q10_api_key },
  });

  let response = undefined;
  while (true) {
    try {
      response = await instance.get(req_link, {
        params: {
          Limit: LIMIT,
          Offset: OFFSET,
        },
      });
    } catch (error) {
      () => {};
    }
    if (response?.status != 200) {
      () => {};
    } else {
      pages = response.headers["x-paging-pagecount"];
      actualPage = response.headers["x-paging-pagenumber"];
      n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      condicion_matriculas.push(...structuredClone(response.data));
    }

    // This API always says it has 0 items, so I'll be breaking after the frist page:
    if (n_items == 0) {
      break;
    } else {
      OFFSET += 1;
    }

    if (pages == actualPage) {
      break;
    } else {
      OFFSET += 1;
    }
  }

  return condicion_matriculas;
}

async function get_and_sync_condicion_matriculas(estado) {
  const condicion_matriculas = await get_condicion_matriculas(estado);

  let futuros = [];
  for (const j in condicion_matriculas) {
    futuros.push(sync_condicion_matricula(condicion_matriculas[j]));
  }
  await Promise.allSettled(futuros).then((results) =>
    results.forEach((result) => {
      if (result.status === "fulfilled") {
        () => {};
      } else {
        logError(`FAILED CONDICION_MATRICULA: ${result?.reason}`);
      }
    })
  );
}

export async function sync_condicion_matriculas() {
  await get_and_sync_condicion_matriculas(false);
  await get_and_sync_condicion_matriculas(true);
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_condicion_matriculas();

  process.exit();
}
