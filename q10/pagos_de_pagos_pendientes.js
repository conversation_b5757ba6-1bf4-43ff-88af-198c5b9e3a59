import axios from "axios";
import { config, q10_api_key } from "./config.js";
import async from "async";
import knex from "knex";

const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const PAGOSPENDIENTES_API_URL = "https://api.q10.com/v1/pagos";
const LIMIT = 400;
let OFFSET = 1;

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

export async function sync_pago(pago) {
  log(
    `Consecutivo_pago: ${pago.Consecutivo_pago} - Numero_recibo_pago: ${pago.Numero_recibo_pago}`
  );

  let nuevo_pago = false;
  await knex_conn("Otro_pago")
    .select()
    .where("Consecutivo_pago", pago.Consecutivo_pago)
    .where("Numero_recibo_pago", pago.Numero_recibo_pago)
    .then(async (rows) => {
      if (rows.length === 0) {
        nuevo_pago = true;
        await knex_conn("Otro_pago").insert({
          Codigo_persona: pago.Codigo_persona,
          Codigo_tipo_identificacion: pago.Codigo_tipo_identificacion,
          Abrevitura_tipo_identificacion: pago.Abrevitura_tipo_identificacion,
          Identificacion_estudiante: pago.Identificacion_estudiante,
          Nombre_estudiante: pago.Nombre_estudiante,
          Consecutivo_pago: pago.Consecutivo_pago,
          Numero_recibo_pago: pago.Numero_recibo_pago,
          Fecha_asiento_pago: pago.Fecha_asiento_pago,
          Fecha_pago: pago.Fecha_pago,
          Valor_pagado: pago.Valor_pagado,
          Anulado: pago.Anulado,
          Codigo_programa: pago.Codigo_programa,
          Nombre_programa: pago.Nombre_programa,
          Observaciones: pago.Observaciones,
        });
      } else {
        await knex_conn("Otro_pago")
          .update({
            Codigo_persona: pago.Codigo_persona,
            Codigo_tipo_identificacion: pago.Codigo_tipo_identificacion,
            Abrevitura_tipo_identificacion: pago.Abrevitura_tipo_identificacion,
            Identificacion_estudiante: pago.Identificacion_estudiante,
            Nombre_estudiante: pago.Nombre_estudiante,
            Consecutivo_pago: pago.Consecutivo_pago,
            Fecha_pago: pago.Fecha_pago,
            Valor_pagado: pago.Valor_pagado,
            Anulado: pago.Anulado,
            Codigo_programa: pago.Codigo_programa,
            Nombre_programa: pago.Nombre_programa,
            Observaciones: pago.Observaciones,
          })
          .where("Consecutivo_pago", pago.Consecutivo_pago)
          .where("Numero_recibo_pago", pago.Numero_recibo_pago);
      }
    });
  return nuevo_pago;
}

export async function sync_pago_forma_pago(pagoData) {
  try {
    await knex_conn.transaction(async (trx) => {
      const formasPago = pagoData.Formas_pago;
      delete pagoData.Formas_pago;
      delete pagoData.Detalles;
      const nuevo_pago = await sync_pago(pagoData);
      if (nuevo_pago) {
        log(
          `Pago #${pagoData.Numero_recibo_pago} es nuevo, agregando a Forma_pago y Detalle_cuota .`
        );
        await Promise.all(
          formasPago.map((formaPago) =>
            trx
              .insert({
                Consecutivo_pago: pagoData.Consecutivo_pago,
                Numero_recibo_pago: pagoData.Numero_recibo_pago,
                ...formaPago,
              })
              .into("Forma_otro_pago")
          )
        );
      }
    });
  } catch (error) {
    log(error);
  }
}

export async function sync_pago_pago_pendiente(pagos) {
  let i = 0;
  for (const pagoData of pagos) {
    i += 1;
    process.stdout.write(`pago: ${i} of  ${pagos.length} - `);
    await sync_pago_forma_pago(pagoData);
  }
}

export async function sync_pagos_de_pagos_pendientes() {
  OFFSET = 1;
  const req_link = PAGOSPENDIENTES_API_URL;

  const instance = axios.create({
    baseURL: req_link,
    timeout: 50000,
    headers: { "API-Key": q10_api_key },
  });

  let done = false;
  while (true) {
    try {
      let response = undefined;
      // Fetch data from the API for each period
      const newDate = new Date();
      newDate.setFullYear(newDate.getFullYear() - 1);
      const initialDateIso = newDate.toISOString().split("T")[0];
      const today = new Date();
      const endDateIso = today.toISOString().split("T")[0];
      while (true) {
        try {
          log(`${req_link} - ${initialDateIso} ${endDateIso}`);
          response = await instance.get(req_link, {
            params: {
              Limit: LIMIT,
              Offset: OFFSET,
              Fecha_inicio: initialDateIso,
              Fecha_fin: endDateIso,
            },
          });
        } catch (error) {
          log(error);
          break;
        }
        if (response?.status != 200) {
          break;
        }
        const pages = response.headers["x-paging-pagecount"];
        const actualPage = response.headers["x-paging-pagenumber"];
        const n_items = response.headers["x-paging-totalitemcount"];
        const n_items_page = response.headers["x-paging-pagesize"];

        log(
          `Total number of pages: ${pages}, items in total: ${n_items}, current page number: ${actualPage}, items on this page ${n_items_page}`
        );

        log(`Got ${response.data.length} pagos.`);
        await sync_pago_pago_pendiente(response.data);

        if (pages == actualPage) {
          done = true;
          break;
        } else {
          OFFSET += 1;
        }
      }
    } catch (error) {
      log(error);
    }
    if (done) {
      break;
    }
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_pagos_de_pagos_pendientes();
  process.exit();
}
