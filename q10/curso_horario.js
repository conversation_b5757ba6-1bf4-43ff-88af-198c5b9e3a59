import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_date_string } from "./primitives.js";
import knex from "knex";

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
    now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

const knex_conn = knex({
  client: "mssql",
  connection: config,
});
function getDates() {
  const today = new Date();

  // Date 4 months ago
  const dateStart = new Date(today);
  dateStart.setMonth(today.getMonth() - 4);

  // Date 1 year in the future
  const dateEnd = new Date(today);
  dateEnd.setFullYear(today.getFullYear() + 1);

  return {
    date_start: dateStart.toISOString(),
    date_end: dateEnd.toISOString()
  };
}

const dates = getDates();

// API URL
const CURSOS_API_URL = "https://api.q10.com/v1/horarios/cursos";
const LIMIT = 800;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

export async function sync_curso_horario() {
  try {
    let response = undefined;
    while (true) {
      try {
        const req_link =
          CURSOS_API_URL +
          `?Limit=${LIMIT}` +
          `&Offset=${OFFSET}` +
          `&Fecha_inicio=${dates.date_start}` +
          `&Fecha_fin=${dates.date_end}` +
          `&Incluir_cursos_archivados=false`;
        log(req_link);
        response = await axios.get(req_link, {
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(`Horario Curso: ${id.Codigo_estudiante} no existe`);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }

      for (const curso of response.data) {
        log(
          `${curso.Codigo_curso} - ${curso.Fecha_inicio} - ${curso.Fecha_fin}`
        );

        await knex_conn("Curso_Horario")
          .select()
          .where("Curso_Horario.Consecutivo_horario", Number(curso.Consecutivo_horario))
          .where("Curso_Horario.Consecutivo_curso", curso.Consecutivo_curso)
          .then(async (rows) => {
            if (rows.length === 0) {
              await knex_conn("Curso_Horario").insert({
                "Consecutivo_horario": curso.Consecutivo_horario,
                "Consecutivo_curso": curso.Consecutivo_curso,
                "Codigo_curso": curso.Codigo_curso,
                "Nombre_curso": curso.Nombre_curso,
                "Codigo_asignatura": curso.Codigo_asignatura,
                "Nombre_asignatura": curso.Nombre_asignatura,
                "Numero_identificacion_docente": curso.Numero_identificacion_docente,
                "Nombre_docente": curso.Nombre_docente,
                "Fecha_inicio": curso.Fecha_inicio,
                "Fecha_fin": curso.Fecha_fin,
                "Consecutivo_aula_fisica": curso.Consecutivo_aula_fisica,
                "Codigo_aula_virtual": curso.Codigo_aula_virtual,
                "Nombre_aula": curso.Nombre_aula,
                "Tipo_aula": curso.Tipo_aula
              });
            } else {
              await knex_conn("Curso_Horario")
                .update({
                  "Consecutivo_horario": curso.Consecutivo_horario,
                  "Consecutivo_curso": curso.Consecutivo_curso,
                  "Codigo_curso": curso.Codigo_curso,
                  "Nombre_curso": curso.Nombre_curso,
                  "Codigo_asignatura": curso.Codigo_asignatura,
                  "Nombre_asignatura": curso.Nombre_asignatura,
                  "Numero_identificacion_docente": curso.Numero_identificacion_docente,
                  "Nombre_docente": curso.Nombre_docente,
                  "Fecha_inicio": curso.Fecha_inicio,
                  "Fecha_fin": curso.Fecha_fin,
                  "Consecutivo_aula_fisica": curso.Consecutivo_aula_fisica,
                  "Codigo_aula_virtual": curso.Codigo_aula_virtual,
                  "Nombre_aula": curso.Nombre_aula,
                  "Tipo_aula": curso.Tipo_aula
                })
                .where("Curso_Horario.Consecutivo_horario", Number(curso.Consecutivo_horario))
                .where("Curso_Horario.Consecutivo_curso", curso.Consecutivo_curso)
            }
          });
      }
    }
  } catch (error) {
    logError("Error:", error);
  } finally {
    // Destroy the connection pool
    await knex_conn.destroy();
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  // Call the function to start fetching and storing data
  await sync_curso_horario();
  process.exit();
}
