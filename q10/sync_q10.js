import { sync_creditos } from "./creditos.js";
import { sync_cursos } from "./cursos.js";
import { sync_curso_horario } from "./curso_horario.js";
import { sync_descuentos } from "./descuentos.js";
import { sync_estudiantes } from "./estudiantes.js";
import { sync_impuestos } from "./impuestos.js";

import { sync_inscripciones } from "./inscripcion.js";
import { sync_ordenes_de_pago } from "./ordenes_de_pago.js";
import { sync_pagos_ordenes_de_pago } from "./pagos_ordenes_de_pago.js";
import { sync_pagos } from "./pagos.js";
import { sync_periodos } from "./periodos.js";
import { sync_programas } from "./programas.js";
import { sync_sede_jornadas } from "./sede_jornada.js";
import { sync_preinscripciones } from "./preinscripciones.js";
import { sync_grupos } from "./grupos.js";
import { sync_condicion_matriculas } from "./condiciones_matricula.js";
import { sync_pagos_de_pagos_pendientes } from "./pagos_de_pagos_pendientes.js";
import { sync_evaluaciones } from "./evaluaciones.js";
import { acquireLock, releaseLock } from "./process_lock.js";
import { sync_comunidad_excel } from "./estudiantes_comunidad_excel.js";
import { log, logError } from "../utils/logging.js";

async function runSync() {
  try {
    if (!acquireLock()) {
      log("Exiting: Another sync process is already running");
      process.exit(1);
    }

    log(" --- start syncing periodos --- ");
    await sync_periodos();
    log(" --- start syncing programas --- ");
    await sync_programas();
    
    log(" --- start syncing estudiantes --- ");
    await sync_estudiantes();
    
    log(" --- start syncing cursos --- ");
    await sync_cursos();
    
    log(" --- start syncing cursos Horarios --- ");
    await sync_curso_horario();
    
    log(" --- start syncing evaluaciones --- ");
    await sync_evaluaciones();
    
    log(" --- start syncing inscripciones --- ");
    await sync_inscripciones();
    
    log(" --- start syncing preinscripciones --- ");
    await sync_preinscripciones();
    
    log(" --- start syncing grupos --- ");
    await sync_grupos();
    
    log(" --- start syncing creditos --- ");
    await sync_creditos();
    
    log(" --- start syncing pagos --- ");
    await sync_pagos();
    
    log(" --- start syncing pagos de pagos pendientes --- ");
    await sync_pagos_de_pagos_pendientes();
    
    log(" --- start syncing pagos ordenes de pago --- ");
    await sync_pagos_ordenes_de_pago();
    
    log(" --- start syncing ordenes de pago --- ");
    await sync_ordenes_de_pago();
    
    log(" --- start syncing impuestos --- ");
    await sync_impuestos();
    
    log(" --- start syncing descuentos --- ");
    await sync_descuentos();
    
    log(" --- start syncing sync_comunidad_excel --- ");
    await sync_comunidad_excel()

    log(" --- done syncing everything --- ");
  } catch (error) {
    logError("Sync process failed:", error);
    releaseLock();
    process.exit(1);
  } finally {
    releaseLock();
  }
  process.exit(0);
}

// Handle process termination
process.on('SIGINT', () => {
  log('Received SIGINT. Cleaning up...');
  releaseLock();
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('Received SIGTERM. Cleaning up...');
  releaseLock();
  process.exit(0);
});

// Start the sync process
runSync();
