// const axios = require("axios");
import axios from "axios";
import { config, q10_api_key } from "./config.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});

const API_URL = "https://api.q10.com/v1/periodos";
const LIMIT = 700;
let OFFSET = 0;

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

export async function sync_periodos() {
  try {
    const response = await axios.get(API_URL, {
      params: { Limit: LIMIT, Offset: OFFSET },
      headers: {
        "API-Key": q10_api_key,
      },
    });

    const data = response.data;
    log(`Total periods = ${data.length}`);
    if (data.length === 0) {
      log("No more data to fetch.");
      await knex_conn.destroy();
      return;
    }

    const periodsData = [];
    const periodsUpdateData = [];
    for (const period of data) {
      const existingPeriod = await knex_conn("periods")
        .select("Consecutivo")
        .where("Consecutivo", period.Consecutivo)
        .first();
      if (!existingPeriod) {
        periodsData.push({
          Consecutivo: period.Consecutivo,
          Nombre: period.Nombre,
          Fecha_inicio: period.Fecha_inicio,
          Fecha_fin: period.Fecha_fin,
          Ordenamiento: period.Ordenamiento,
          Estado: period.Estado,
        });
      } else {
        log(`Updating period ${period.Nombre}`)
        await knex_conn("periods")
          .update({
            Nombre: period.Nombre,
            Fecha_inicio: period.Fecha_inicio,
            Fecha_fin: period.Fecha_fin,
            Ordenamiento: period.Ordenamiento,
            Estado: period.Estado,
          })
          .where("Consecutivo", period.Consecutivo);
      }
    }

    // console.log(JSON.stringify(periodsData));
    const chunkSize = 10;
    let periodChunk = [];
    for (let i = 0; i < periodsData.length; i += chunkSize) {
      periodChunk = periodsData.slice(i, i + chunkSize);
      await knex_conn.transaction(async (trx) => {
        await trx("periods").insert(periodChunk);
      });
    }

    log(`Fetched and stored ${data.length} records.`);
    OFFSET += LIMIT;

    if (data.length === LIMIT) {
      await sync_periodos();
    }
  } catch (error) {
    logError("Error fetching data:", error);
    await knex_conn.destroy();
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  try {
    await sync_periodos();
    process.exit();
  } catch (error) {
    logError("Error creating table or fetching data:", error);
    await knex_conn.destroy();
  }
}
