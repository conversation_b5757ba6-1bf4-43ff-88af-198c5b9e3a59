import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_date_string } from "./primitives.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const ESTUDIANTES_API_URL = "https://api.q10.com/v1/estudiantes/";
const CURSOS_API_URL = "https://api.q10.com/v1/cursos/";
const INSCRIPTOS_API_URL = "https://api.q10.com/v1/estudiantes";
const LIMIT = 800;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

async function get_student(id) {
  let response = undefined;
  try {
    response = await axios.get(ESTUDIANTES_API_URL + id, {
      params: {
        Limit: LIMIT,
        Offset: OFFSET,
      },
      headers: {
        "API-Key": q10_api_key,
      },
    });
  } catch (error) {
    log(`FAILED estudiante URL: ${ESTUDIANTES_API_URL + id}`);
    throw error;
  }

  if (response?.status != 200) {
    log(response);
    return null;
  }

  return response.data;
}

async function update_gender(id, genero) {
  log(`${id} -- ${genero}`);
  await knex_conn("Estudiante")
    .select()
    .where("Codigo_estudiante", id)
    .then(async (rows) => {
      if (rows.length === 0) {
        log(`This student was not registered: ${id}`);
        let student = undefined;
        try {
          student = await get_student(id);
        } catch (error) {
          throw error;
        }
        await knex_conn("Estudiante").insert({
          Codigo_estudiante: student.Codigo_estudiante,
          Primer_nombre: student.Primer_nombre,
          Segundo_nombre: student.Segundo_nombre,
          Primer_apellido: student.Primer_apellido,
          Segundo_apellido: student.Segundo_apellido,
          Numero_identificacion: student.Numero_identificacion,
          Email: student.Email,
          Direccion: student.Direccion,
          Telefono: student.Telefono,
          Celular: student.Celular,
          Genero: student?.Genero,
        });
        // await knex_conn("test_Estudiante").insert({
        //   Codigo_estudiante: id,
        //   Genero: genero,
        // });
        // // throw new Error(`Estudiante ${id} was not synced already.`)
      } else {
        await knex_conn("Estudiante")
          .update({
            Genero: genero,
          })
          .where("Codigo_estudiante", id);
      }
    });
}

async function get_estudiantes(curso) {
  const search_url =
    INSCRIPTOS_API_URL +
    `?Periodo=${curso.Consecutivo_periodo}` +
    // `&Sede_jornada=${curso.Consecutivo_sede_jornada}` +
    // `&Programa=${curso.Codigo_programa}` +
    // `&Curso=${curso.Consecutivo}` +
    `&Limit=${LIMIT}` +
    `&Offset=${OFFSET}`;

  let response = undefined;
  try {
    response = await axios.get(search_url, {
      params: {
        Limit: LIMIT,
        Offset: OFFSET,
      },
      headers: {
        "API-Key": q10_api_key,
      },
    });
    if (response?.status == 200) {
      return response.data;
    }
  } catch (error) {
    if (error?.response?.data?.code === "404") {
      log(
        `${error?.response?.data?.message} Periodo=${curso.Consecutivo_periodo} - Sede_jornada=${curso.Consecutivo_sede_jornada} - id_curso=${curso.Consecutivo}`
      );
    } else {
      log(`Failed link: ${search_url}`);
    }
  }
  return [];
}

export async function sync_genders() {
  try {
    let response = undefined;
    while (true) {
      try {
        // Fetch all courses
        response = await axios.get(CURSOS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(`Estudiante: ${id.Codigo_estudiante} no existe`);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }

      for (const curso of response.data) {
        const inscriptos = await get_estudiantes(curso);
        if (inscriptos.length === 0) {
          continue;
        }
        log(`Periodo= ${curso.Consecutivo_periodo}`);
        // process.stdout.write(
        //   `Sede_jornada= ${curso.Consecutivo_sede_jornada} - `
        // );
        // process.stdout.write(`id_curso= ${curso.Consecutivo} - `);
        // process.stdout.write(`Programa= ${curso.Codigo_programa}\n`);

        let futuros = [];
        for (const inscripto of inscriptos) {
          futuros.push(
            update_gender(inscripto.Codigo_estudiante, inscripto.Genero)
          );
        }

        let results = undefined;
        try {
          results = await Promise.allSettled(futuros);
        } catch (error) {
          log("At least 1 of students of this course failed to sync.");
        }
        // Record the ones that failed.
        const failed_inscriptos = results.filter(
          (p) => p.status === "rejected"
        );
        for (const i in failed_inscriptos) {
          const failed_inscripto = failed_inscriptos[i];
          log(`FAILED INSCRIPTO: ${failed_inscripto?.reason}`);
        }
      }
    }
  } catch (error) {
    logError("Error:", error);
  } finally {
    // Destroy the connection pool
    await knex_conn.destroy();
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_genders();
  process.exit();
}
