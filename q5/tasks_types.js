import { usr as configUsr, psw as configPsw } from "./config.js"; // Renamed to avoid conflict if usr, psw are params
import {
  get_token,
  close_connection,
} from "./primitives.js";

import {
  add_task_type,
  list_pageof_task_types,
} from "./utils_tasks.js";

// Define a constant for the concurrency limit for add_task_type calls
const MAX_CONCURRENT_ADDS = 10; // Adjust this value as needed

export async function sync_task_types(usr, psw) {
  let token;
  try {
    token = await get_token(usr, psw);
  } catch (error) {
    console.error("Failed to get token:", error);
    throw new Error(`Authentication failed: ${error.message}`);
  }

  // Stores objects like { promise: Promise, task_name: string }
  let pending_operations = [];
  // Stores final results like { status: 'fulfilled'|'rejected', value?: any, reason?: any, task_name: string }
  const all_processed_results = [];

  let current_page_items;
  let next_page_exists_indicator;
  let page_num = 0;

  while (true) {
    page_num++;
    try {
      // Assuming list_pageof_task_types(token) gets the next page of items
      // and an indicator if more pages exist.
      [current_page_items, next_page_exists_indicator] = await list_pageof_task_types(token);
      console.log(`Fetched page ${page_num} of task types and length ${current_page_items?.length}`);
    } catch (error) {
      console.error(`Error fetching page ${page_num} of task types:`, error);
      break; // Stop processing if fetching a page fails
    }

    if (!current_page_items || current_page_items.length === 0) {
      if (next_page_exists_indicator == null) {
        // No items on this page and no indicator of a next page.
        break;
      } else {
        // Empty page, but API indicates more pages might be available.
        // Loop will continue and call list_pageof_task_types again.
        continue;
      }
    }

    for (const task_type of current_page_items) {
      let task_name_for_logging = "Unknown/Invalid";
      if (task_type && typeof task_type.name === 'string' && task_type.name.trim() !== '') {
        task_name_for_logging = task_type.name;
      } else {
        console.warn("Encountered an invalid task_type object or task_type without a name, skipping:", task_type);
        all_processed_results.push({
            status: "rejected",
            reason: "Invalid task_type data (object or name missing)",
            task_name: task_name_for_logging
        });
        continue;
      }

      let promise;
      try {
        promise = add_task_type(task_type); // Expected to return a Promise
        if (!promise || typeof promise.then !== 'function') {
            console.error(`CRITICAL: add_task_type for ${task_name_for_logging} did not return a promise. Skipping.`);
            all_processed_results.push({
              status: "rejected",
              reason: `add_task_type for ${task_name_for_logging} did not return a promise.`,
              task_name: task_name_for_logging
            });
            continue;
        }
      } catch (syncError) {
        console.error(`Synchronous error while queuing task type: ${task_name_for_logging}`, syncError);
        all_processed_results.push({
          status: "rejected",
          reason: `Sync error for ${task_name_for_logging}: ${syncError.message}`,
          task_name: task_name_for_logging
        });
        continue;
      }
      
      pending_operations.push({ promise: promise, task_name: task_name_for_logging });

      if (pending_operations.length >= MAX_CONCURRENT_ADDS) {
        const promises_to_settle = pending_operations.map(op => op.promise);
        const task_names_for_batch = pending_operations.map(op => op.task_name);
        
        const batch_settled_results = await Promise.allSettled(promises_to_settle);
        
        batch_settled_results.forEach((result, index) => {
          all_processed_results.push({ 
            ...result, 
            task_name: task_names_for_batch[index] 
          });
        });
        pending_operations = []; // Reset for the next batch
      }
    }

    if (next_page_exists_indicator == null) {
      // This was the last page of items.
      break;
    }
  }

  // Process any remaining operations in the last batch
  if (pending_operations.length > 0) {
    const promises_to_settle = pending_operations.map(op => op.promise);
    const task_names_for_batch = pending_operations.map(op => op.task_name);
    const batch_settled_results = await Promise.allSettled(promises_to_settle);
    batch_settled_results.forEach((result, index) => {
      all_processed_results.push({ ...result, task_name: task_names_for_batch[index] });
    });
  }

  // Log failures and summary
  let success_count = 0;
  let failure_count = 0;
  all_processed_results.forEach(result => {
    if (result.status === "rejected") {
      failure_count++;
      const reason_message = result.reason instanceof Error ? result.reason.message : String(result.reason);
      console.error(`FAILED TASK TYPE SYNC: Name: '${result.task_name}', Reason: ${reason_message}`);
      // For more detailed debugging, you could log result.reason if it's an error object:
      // if (result.reason instanceof Error) { console.error(result.reason); }
    } else {
      success_count++;
    }
  });

  console.log(`--- Syncing task types complete ---`);
  console.log(`Total processed: ${all_processed_results.length}, Successfully synced: ${success_count}, Failed: ${failure_count}`);
  
  return token;
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  // Use credentials from config when running directly
  const token = await sync_task_types(configUsr, configPsw);
  await close_connection(token);
  process.exit();
}
