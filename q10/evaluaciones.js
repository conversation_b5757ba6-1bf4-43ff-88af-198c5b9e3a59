import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_datetime_string } from "./primitives.js";
import knex from "knex";
import async from "async";
import fs from "fs/promises"; // Import fs promises API
import path from "path"; // Optional: for robust path handling

const knex_conn = knex({
  client: "mssql",
  connection: config,
  pool: {
    min: 0,
    max: 10,
    idleTimeoutMillis: 60000, // 1 minute idle timeout
    acquireTimeoutMillis: 60000, // 1 minute acquire timeout
    createTimeoutMillis: 30000, // 30 seconds create timeout
    createRetryIntervalMillis: 200, // Time between retries
  },
});

// API URL
const CURSOS_API_URL = "https://api.q10.com/v1/cursos";
const PROGRAMAS_API_URL = "https://api.q10.com/v1/programas";
const EVALUACIONES_API_URL = "https://api.q10.com/v1/evaluaciones";

const LIMIT = 5000;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

// --- Memory cache for curso details ---
const cursoCache = new Map(); // Map for storing curso details by key
let cacheHits = 0;
let cacheMisses = 0;
const MAX_CACHE_SIZE = 10000; // Maximum number of items to keep in cache

// We use string template literals directly for cache keys: `${Consecutivo_curso}_${Consecutivo_periodo}`

// Simple LRU-like cache management - remove oldest entries when cache gets too large
function manageCacheSize() {
  if (cursoCache.size > MAX_CACHE_SIZE) {
    // Get the first N keys to remove (oldest entries in insertion order)
    const keysToRemove = Array.from(cursoCache.keys()).slice(
      0,
      Math.floor(MAX_CACHE_SIZE * 0.2)
    ); // Remove 20% of cache
    log(
      `Cache size limit reached (${cursoCache.size}). Removing ${keysToRemove.length} oldest entries.`
    );

    // Remove the keys
    keysToRemove.forEach((key) => cursoCache.delete(key));
  }
}

// --- File path for tracking processed programs ---
const PROCESSED_PROGRAMAS_FILE = path.resolve("./processed_programas.json"); // Use absolute path
const PROCESSED_FILE_MAX_AGE_HOURS = 24; // Reset processed list after this many hours

// --- Helper functions for logging ---
function getISODateTime() {
  const now = new Date();
  // Correctly format milliseconds
  return now.toISOString().replace("T", " ").slice(0, -1);
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

// --- Helper functions for managing processed programs file ---
async function loadProcessedProgramas() {
  try {
    // Check if file exists
    await fs.access(PROCESSED_PROGRAMAS_FILE);

    // Check file age
    const stats = await fs.stat(PROCESSED_PROGRAMAS_FILE);
    const fileAgeHours = (Date.now() - stats.mtime) / (1000 * 60 * 60);

    // If file is older than max age, return empty set
    if (fileAgeHours > PROCESSED_FILE_MAX_AGE_HOURS) {
      log(
        `File ${PROCESSED_PROGRAMAS_FILE} is older than ${PROCESSED_FILE_MAX_AGE_HOURS} hours. Starting fresh.`
      );
      return new Set();
    }

    const data = await fs.readFile(PROCESSED_PROGRAMAS_FILE, "utf-8");
    const parsedData = JSON.parse(data);
    if (!Array.isArray(parsedData)) {
      logError(
        `Invalid format in ${PROCESSED_PROGRAMAS_FILE}. Expected an array. Starting fresh.`
      );
      return new Set();
    }
    log(
      `Loaded ${parsedData.length} processed program codes from ${PROCESSED_PROGRAMAS_FILE}`
    );
    return new Set(parsedData); // Use a Set for efficient lookups
  } catch (error) {
    if (error.code === "ENOENT") {
      log(`File ${PROCESSED_PROGRAMAS_FILE} not found. Starting fresh.`);
      return new Set(); // File doesn't exist, return empty set
    } else if (error instanceof SyntaxError) {
      logError(
        `Error parsing JSON from ${PROCESSED_PROGRAMAS_FILE}:`,
        error,
        `Starting fresh.`
      );
      return new Set(); // Invalid JSON, return empty set
    } else {
      logError(
        `Error loading processed programs file (${PROCESSED_PROGRAMAS_FILE}):`,
        error
      );
      logError(
        "Proceeding without loading previously processed programs due to error."
      );
      return new Set();
    }
  }
}

async function saveProcessedProgramas(processedSet) {
  try {
    const dataArray = Array.from(processedSet); // Convert Set to Array for JSON
    await fs.writeFile(
      PROCESSED_PROGRAMAS_FILE,
      JSON.stringify(dataArray, null, 2)
    ); // Pretty print JSON
    // log(`Saved ${dataArray.length} processed program codes to ${PROCESSED_PROGRAMAS_FILE}`); // Optional: Log on every save
  } catch (error) {
    logError(
      `Error saving processed programs file (${PROCESSED_PROGRAMAS_FILE}):`,
      error
    );
    // Decide how to handle save errors. Maybe retry?
  }
}

async function sync_evaluacion(evaluacion) {
  // Some evaluaciones don't come from actual cursos
  // log(evaluacion)
  // Note: We directly use evaluacion.Consecutivo_curso and evaluacion.Consecutivo_periodo in the get_curso_details call
  // No need to create separate variables
  // if (evaluacion.Consecutivo_curso == null || evaluacion.Consecutivo_periodo == null ||
  //     evaluacion.Consecutivo_curso == 0 || evaluacion.Consecutivo_periodo == 0) {
  //   log(`${evaluacion.Codigo_estudiante} -- ${evaluacion.Codigo_asignatura} No tiene consecutivo de curso `);
  //   return false
  // }

  // Use try-catch for robustness when getting curso details
  let curso;
  try {
    const consecutivoCurso =
      evaluacion.Consecutivo_curso == null ? 0 : evaluacion.Consecutivo_curso;
    const consecutivoPeriodo =
      evaluacion.Consecutivo_periodo == null
        ? 0
        : evaluacion.Consecutivo_periodo;
    curso = await get_curso_details(consecutivoCurso, consecutivoPeriodo);
  } catch (error) {
    logError(
      `Error getting curso details for C:${evaluacion.Consecutivo_curso}, P:${evaluacion.Consecutivo_periodo}. Skipping sync for evaluacion: ${evaluacion.Codigo_estudiante}/${evaluacion.Codigo_asignatura}`,
      error
    );
    return false; // Indicate failure
  }

  const last_update = to_cr_datetime_string(new Date());

  log(
    `${evaluacion.Codigo_estudiante} -- ${
      evaluacion.Codigo_asignatura
    }  - curso: ${curso?.Consecutivo ?? "N/A"}`
  ); // Use nullish coalescing

  try {
    // Check if a record with this composite primary key already exists
    // The primary key consists of id_estudiante, Consecutivo_pensum, and Codigo_asignatura
    const rows = await knex_conn("Evaluacion")
      .select("id_estudiante") // Select only necessary column for existence check
      .where("id_estudiante", evaluacion.Codigo_estudiante)
      .where("Codigo_asignatura", evaluacion.Codigo_asignatura)
      .where("Consecutivo_pensum", evaluacion.Consecutivo_pensum)
      .where("Consecutivo_periodo", evaluacion.Consecutivo_periodo)
      .where(
        "Estado_matricula_asignatura",
        evaluacion.Estado_matricula_asignatura
      )
      .first(); // Use first() for checking existence, more efficient

    // Prepare data for insert/update, excluding primary key fields that are used in the WHERE clause
    const dataToUpsert = {
      Consecutivo_curso: curso?.Consecutivo, // Use optional chaining
      Programa_codigo: evaluacion.Codigo_programa,
      Consecutivo_sede_jornada: curso?.Consecutivo_sede_jornada, // Use optional chaining
      Codigo_curso: evaluacion.Codigo_curso,
      Nombre_completo: evaluacion.Nombre_completo,
      Nombre_asignatura: evaluacion.Nombre_asignatura,
      Nombre_periodo: evaluacion.Nombre_periodo,
      Nombre_programa: evaluacion.Nombre_programa,
      Nombre_curso: evaluacion.Nombre_curso,
      Codigo_matricula: evaluacion.Codigo_matricula,
      Promedio_evaluacion: evaluacion.Promedio_evaluacion,
      Porcentaje_evaluado: evaluacion.Porcentaje_evaluado,
      Porcentaje_inasistencia: evaluacion.Porcentaje_inasistencia,
      Cantidad_inasistencia: evaluacion.Cantidad_inasistencia,
      Promedio_acumulado: evaluacion.Promedio_acumulado,
      Promedio_periodo: evaluacion.Promedio_periodo,
      Nombre_pensum: evaluacion.Nombre_pensum,
      Ultima_actualizacion: last_update,
      Numero_identificacion: evaluacion.Numero_identificacion,
      Estado_matricula_asignatura: evaluacion.Estado_matricula_asignatura,
      // Note: We're not including the primary key fields in dataToUpsert
      // because they're already in the WHERE clause for updates
      // or will be explicitly added for inserts
    };

    if (!rows) {
      // Insert - need to include ALL primary key fields
      await knex_conn("Evaluacion").insert({
        // Include all primary key fields
        id_estudiante: evaluacion.Codigo_estudiante,
        Consecutivo_pensum: evaluacion.Consecutivo_pensum,
        Codigo_asignatura: evaluacion.Codigo_asignatura,
        Consecutivo_periodo: evaluacion.Consecutivo_periodo,
        // Include all other fields
        ...dataToUpsert,
      });
      log(
        `Inserted evaluation for ${evaluacion.Codigo_estudiante} - ${evaluacion.Codigo_asignatura} - Pensum: ${evaluacion.Consecutivo_pensum}`
      );
    } else {
      // Update - must specify ALL primary key fields in the WHERE clause
      await knex_conn("Evaluacion")
        .update(dataToUpsert)
        .where("id_estudiante", evaluacion.Codigo_estudiante)
        .where("Codigo_asignatura", evaluacion.Codigo_asignatura)
        .where("Consecutivo_periodo", evaluacion.Consecutivo_periodo)
        .where("Consecutivo_pensum", evaluacion.Consecutivo_pensum);
      log(
        `Updated evaluation for ${evaluacion.Codigo_estudiante} - ${evaluacion.Codigo_asignatura}`
      );
    }
    return true; // Indicate success
  } catch (dbError) {
    // Log more detailed error information
    logError(
      `Database error syncing evaluation for ${evaluacion.Codigo_estudiante} - ${evaluacion.Codigo_asignatura} - Pensum: ${evaluacion.Consecutivo_pensum}:`,
      dbError
    );

    // Add more specific error handling for duplicate key errors
    if (
      dbError.code === "EREQUEST" &&
      dbError.message.includes("Cannot insert duplicate key")
    ) {
      logError(
        `Duplicate key error detected. This means another record with the same primary key (id_estudiante, Codigo_asignatura, Consecutivo_pensum) already exists.`,
        evaluacion
      );
      // You could implement a retry with a different approach here if needed
    }

    return false; // Indicate failure
  }
}

// Function to get default curso structure for invalid inputs
function getDefaultCursoStructure(
  Consecutivo_curso = null,
  Consecutivo_periodo = null
) {
  return {
    Consecutivo: Consecutivo_curso, // Indicate invalidity clearly but keep the ID
    Consecutivo_periodo: Consecutivo_periodo,
    Codigo: null,
    Nombre: null,
    Codigo_docente: null,
    Abreviatura_tipo_identificacion_docente: null,
    Numero_identificacion_docente: null,
    Nombre_docente: null,
    Consecutivo_sede_jornada: null,
    Nombre_sede_jornada: null,
    Codigo_programa: null,
    Nombre_programa: null,
    Consecutivo_pensum_programa: null,
    Nombre_pensum_programa: null,
    Codigo_asignatura: null,
    Nombre_asignatura: null,
    Cupo_maximo: null,
    Cantidad_estudiantes_matriculados: null,
    Nombre_periodo: null,
    Fecha_inicio: null,
    Fecha_fin: null,
    Estado: null,
    Aplica_matricula_en_linea: false,
    Consecutivo_descuento: null,
    Nombre_descuento: null,
    Fecha_inicio_descuento: null,
    Fecha_fin_descuento: null,
    Docentes_apoyo: [],
  };
}

// Get cache statistics
export function getCursoCacheStats() {
  return {
    size: cursoCache.size,
    hits: cacheHits,
    misses: cacheMisses,
    hitRatio: cacheHits / (cacheHits + cacheMisses || 1), // Avoid division by zero
  };
}

// Clear the curso cache
export function clearCursoCache() {
  const oldSize = cursoCache.size;
  cursoCache.clear();
  cacheHits = 0;
  cacheMisses = 0;
  log(`Curso cache cleared. ${oldSize} items removed.`);
  return { cleared: true, previousSize: oldSize };
}

// Get curso details with caching
export async function get_curso_details(
  Consecutivo_curso,
  Consecutivo_periodo
) {
  // Return a default structure immediately if inputs are invalid
  if (
    Consecutivo_curso == null ||
    Consecutivo_periodo == null ||
    Consecutivo_curso == 0 ||
    Consecutivo_periodo == 0
  ) {
    // log(`Invalid Consecutivo_curso (${Consecutivo_curso}) or Consecutivo_periodo (${Consecutivo_periodo}) provided. Returning default structure.`);
    return getDefaultCursoStructure(Consecutivo_curso, Consecutivo_periodo);
  }

  // Generate cache key
  const cacheKey = `${Consecutivo_curso}_${Consecutivo_periodo}`;

  // Check if we have this curso in the cache
  if (cursoCache.has(cacheKey)) {
    cacheHits++;
    // Log cache hit every 100 hits to avoid excessive logging
    if (cacheHits % 100 === 0) {
      log(
        `Cache stats: ${cacheHits} hits, ${cacheMisses} misses, ${cursoCache.size} cached items`
      );
    }
    return cursoCache.get(cacheKey);
  }

  // Cache miss - need to fetch from API
  cacheMisses++;

  // No need for OFFSET = 0 here, it's a detail endpoint
  const cursoDetailUrl = `${CURSOS_API_URL}/${Consecutivo_curso}`;
  log(`Cache miss. Fetching curso details from: ${cursoDetailUrl}`);

  try {
    // Fetch specific course by Consecutivo_curso
    // The API documentation implies GET /v1/cursos/{Consecutivo_curso} doesn't use Limit/Offset
    const response = await axios.get(cursoDetailUrl, {
      // params: { Limit: 1, Offset: 1 }, // Usually not needed for specific resource GET
      headers: { "API-Key": q10_api_key },
      timeout: 30000, // Add a reasonable timeout
    });

    if (response?.status === 200 && response.data) {
      // Ensure the returned curso has the correct period, override if necessary
      // Although usually the API should return the correct one based on the Consecutivo_curso
      if (response.data.Consecutivo_periodo !== Consecutivo_periodo) {
        log(
          `Warning: Curso detail for ${Consecutivo_curso} returned Periodo ${response.data.Consecutivo_periodo}, but expected ${Consecutivo_periodo}. Using returned data.`
        );
        // Decide if you should override or trust the API response. Trusting API is usually better.
        // response.data.Consecutivo_periodo = Consecutivo_periodo;
      }

      // Store in cache before returning
      cursoCache.set(cacheKey, response.data);

      // Check if we need to manage cache size
      manageCacheSize();

      // Log cache size every 100 misses
      if (cacheMisses % 100 === 0) {
        log(
          `Cache stats: ${cacheHits} hits, ${cacheMisses} misses, ${cursoCache.size} cached items`
        );
      }

      return response.data;
    } else {
      logError(
        `Failed to get curso details for ${Consecutivo_curso}. Status: ${response?.status}. Returning default structure.`
      );
      // Return the default structure if fetch fails but doesn't throw
      const defaultStructure = getDefaultCursoStructure(
        Consecutivo_curso,
        Consecutivo_periodo
      );
      // Don't cache failed responses
      return defaultStructure;
    }
  } catch (error) {
    // Handle specific axios errors if needed (e.g., 404 Not Found)
    if (error.response?.status === 404) {
      log(
        `Warning: Curso with Consecutivo_curso ${Consecutivo_curso} not found (404).`
      );
    } else {
      logError(
        `Error fetching curso details for ${Consecutivo_curso}:`,
        error.code || error.message
      );
    }
    // Return a minimal structure indicating failure but including IDs
    // Don't cache error responses
    return getDefaultCursoStructure(Consecutivo_curso, Consecutivo_periodo);
  }
}

export async function list_programas() {
  OFFSET = 1; // Reset offset for this function
  let programas = [];
  let totalPages = 1; // Assume at least one page initially

  log("Starting to list programas...");

  try {
    while (OFFSET <= totalPages) {
      // Use OFFSET based loop control
      log(`Fetching programas page: ${OFFSET}`);
      let response;
      try {
        response = await axios.get(PROGRAMAS_API_URL, {
          params: { Limit: LIMIT, Offset: OFFSET, Estado: "true" },
          headers: { "API-Key": q10_api_key },
          timeout: 60000, // Increase timeout for potentially large listsa
        });
      } catch (error) {
        logError(
          `Error fetching programas page ${OFFSET}:`,
          error.code || error.message,
          "Retrying or skipping might be needed."
        );
        // Optional: Implement retry logic here or break/continue
        await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait 5s before potentially retrying
        continue; // Simple retry by re-running the current loop iteration
      }

      if (response?.status === 200 && response.data) {
        // Update total pages only once from the first successful response headers
        if (OFFSET === 1) {
          totalPages = parseInt(
            response.headers["x-paging-pagecount"] || "1",
            10
          );
          const totalItems =
            response.headers["x-paging-totalitemcount"] || "N/A";
          log(
            `Total programas pages: ${totalPages}, Total items: ${totalItems}`
          );
        }

        const itemsOnPage = response.data.length;
        log(
          `Fetched page ${OFFSET}/${totalPages}. Items on this page: ${itemsOnPage}`
        );

        if (itemsOnPage > 0) {
          // Use structuredClone if deep copies are truly needed, otherwise spread is fine
          // programas.push(...structuredClone(response.data));
          programas.push(...response.data);
        } else if (OFFSET > 1) {
          // If a later page returns 0 items unexpectedly, log it but break
          log(`Warning: Page ${OFFSET} returned 0 items, stopping pagination.`);
          break;
        }

        // Check if it was the last page based on items received vs limit, or header info
        if (itemsOnPage < LIMIT || OFFSET >= totalPages) {
          log("Reached the end of programas pagination.");
          break;
        }

        OFFSET += 1; // Increment offset for the next page
      } else {
        logError(
          `Failed to fetch programas page ${OFFSET}. Status: ${response?.status}. Stopping pagination.`
        );
        break; // Stop if a page fetch fails
      }
      // Optional delay between requests to avoid rate limiting
      // await new Promise(resolve => setTimeout(resolve, 500));
    }
  } catch (error) {
    // Catch errors not handled within the loop (e.g., initial setup errors)
    logError("An unexpected error occurred during programa listing:", error);
  }

  log(`Finished listing programas. Total found: ${programas.length}`);
  return programas;
}

// --- Modified function ---
export async function get_and_sync_evaluaciones(programas) {
  // Load the set of already processed program codes
  const processedProgramas = await loadProcessedProgramas();
  let newProgramasProcessedCount = 0;
  let totalPending = programas.length - processedProgramas.size;

  log(
    `Starting evaluation sync for ${programas.length} programs. ${processedProgramas.size} already processed. ${totalPending} pending.`
  );

  try {
    // Use a standard for...of loop for better async/await handling
    for (const programa of programas) {
      const programaCodigo = programa.Codigo;
      const onlyProcessTheseProgramas = [];
      // Check if this program code is already in our processed list
      if (processedProgramas.has(programaCodigo)) {
        log(`Skipping already processed programa: ${programaCodigo}`);
        continue; // Move to the next program
      }
      if (
        onlyProcessTheseProgramas.length > 0 &&
        !onlyProcessTheseProgramas.includes(programaCodigo)
      ) {
        log(
          `Program not included in the manual set of Programs to check: ${programaCodigo}`
        );
        continue; // Move to the next program
      }

      log(
        `Processing programa: ${programaCodigo} (${
          newProgramasProcessedCount + 1
        }/${totalPending})`
      );

      try {
        // Get evaluations for this program
        let evaluaciones;
        try {
          evaluaciones = await get_evaluaciones(programaCodigo);
          if (!evaluaciones || evaluaciones.length === 0) {
            log(
              `No evaluations found for programa ${programaCodigo}. Marking as processed.`
            );
            // Mark as processed even if no evaluations, to avoid re-checking
            processedProgramas.add(programaCodigo);
            await saveProcessedProgramas(processedProgramas);
            newProgramasProcessedCount++;
            continue; // Move to the next program
          }
        } catch (error) {
          logError(
            `ERROR. Could not get evaluations for ${programaCodigo}. Skipping this program WITHOUT marking as processed.`,
            error
          );
          // Do NOT mark as processed when there's an error fetching evaluations
          continue; // Skip this program on error
        }

        log(
          `Syncing ${evaluaciones.length} evaluations for programa ${programaCodigo}...`
        );

        // Process evaluations with async.mapLimit and wait for completion
        const results = await new Promise((resolve, reject) => {
          async.mapLimit(
            evaluaciones,
            10,
            async (evaluacion) => {
              return await sync_evaluacion(evaluacion);
            },
            (err, results) => {
              if (err) {
                reject(err);
              } else {
                resolve(results);
              }
            }
          );
        });

        // Check for failed syncs
        const failedSyncs = results.filter((result) => result === false).length;
        if (failedSyncs > 0) {
          throw new Error(
            `${failedSyncs} evaluations failed for ${programaCodigo}`
          );
        }

        // If we get here, all evaluations synced successfully
        log(
          `Successfully synced all ${evaluaciones.length} evaluations for ${programaCodigo}.`
        );
        processedProgramas.add(programaCodigo);
        await saveProcessedProgramas(processedProgramas);
        newProgramasProcessedCount++;
        log(`--- Program ${programaCodigo} finished ---`);
      } catch (error) {
        // Handle any errors during processing of this program
        logError(
          `Processing for programa ${programaCodigo} failed or had errors. It will not be marked as processed. Error: ${error.message}`
        );
        log(`--- Program ${programaCodigo} failed ---`);
        // Continue to the next program without marking this one as processed
      }
    }
  } catch (error) {
    logError(
      "An unexpected error occurred during the main evaluation sync loop:",
      error
    );
  } finally {
    log(
      `Evaluation sync process finished. ${newProgramasProcessedCount} new programs processed in this run. ${
        totalPending - newProgramasProcessedCount
      } still pending.`
    );
  }
}

export async function get_evaluaciones(programa) {
  let evaluaciones = [];
  OFFSET = 1; // Reset offset for each program
  const req_link = `${EVALUACIONES_API_URL}?Programa=${programa}`; // Use template literal
  let totalPages = 1; // Assume at least one page

  log(`Fetching evaluations for Programa: ${programa} from ${req_link}`);

  // Create instance per request or reuse a base instance if headers/timeouts are constant
  const instance = axios.create({
    // baseURL: EVALUACIONES_API_URL, // Base URL can be set here
    timeout: 500000, // Long timeout, consider if this is necessary or could be shorter
    headers: { "API-Key": q10_api_key },
  });

  try {
    while (OFFSET <= totalPages) {
      log(`Fetching evaluations page ${OFFSET} for ${programa}`);
      let response;
      try {
        // Pass params directly to instance.get
        response = await instance.get(EVALUACIONES_API_URL, {
          // Use base URL if set in create()
          params: { Programa: programa, Limit: LIMIT, Offset: OFFSET },
        });
      } catch (error) {
        logError(
          `Error fetching evaluations page ${OFFSET} for ${programa}:`,
          error.code || error.message
        );
        // Implement retry or break logic
        if (error.response?.status === 429) {
          // Too Many Requests
          logError("Rate limit hit. Waiting before retrying...");
          await new Promise((resolve) => setTimeout(resolve, 3000)); // Wait 3s
          continue; // Retry the same page
        }
        // For any other error, throw to be caught by the outer catch block
        // This ensures we don't return a partial result when there's an error
        throw error;
      }

      if (response?.status === 200 && response.data) {
        // Update total pages from headers on first successful request
        if (OFFSET === 1) {
          totalPages = parseInt(
            response.headers["x-paging-pagecount"] || "1",
            10
          );
          const totalItems =
            response.headers["x-paging-totalitemcount"] || "N/A"; // Q10 API might be unreliable here
          log(
            `Total evaluation pages for ${programa}: ${totalPages}, Total items header: ${totalItems}`
          );
          // Handle the case where the API reports 0 total items but returns data
          if (totalItems === "0" && response.data.length > 0) {
            log(
              `Warning: API reported 0 total items for ${programa} but returned data on page 1. Proceeding page by page.`
            );
            // We might need to rely solely on getting an empty page back
          }
        }

        const itemsOnPage = response.data.length;
        log(
          `Fetched page ${OFFSET}/${totalPages} for ${programa}. Items: ${itemsOnPage}`
        );

        if (itemsOnPage > 0) {
          evaluaciones.push(...response.data); // Use spread syntax
          log(
            `Total evaluations collected so far for ${programa}: ${evaluaciones.length}`
          );
        } else {
          // If we get an empty page, assume we are done, regardless of headers
          log(
            `Page ${OFFSET} for ${programa} is empty. Assuming end of evaluations.`
          );
          break;
        }

        // Decide when to stop pagination
        // Option 1: Trust the headers (if reliable)
        // if (OFFSET >= totalPages) break;
        // Option 2: Stop when fewer items than LIMIT are returned (safer if headers unreliable)
        // if (itemsOnPage < LIMIT) break;
        // Option 3: Stop when an empty page is returned (used above)

        // Break if we've reached the reported number of pages (if trusting headers)
        if (OFFSET >= totalPages) {
          log(`Reached reported last page (${totalPages}) for ${programa}.`);
          break;
        }

        OFFSET += 1; // Increment for next page
      } else {
        logError(
          `Failed to fetch evaluations page ${OFFSET} for ${programa}. Status: ${response?.status}. Stopping.`
        );
        break;
      }
      // Optional delay
      // await new Promise(resolve => setTimeout(resolve, 500));
    }
  } catch (error) {
    logError(
      `An unexpected error occurred fetching evaluations for ${programa}:`,
      error.code
    );
    // Re-throw the error to be caught by the caller
    // This ensures the program is not marked as processed when there's an error
    throw error;
  }

  log(
    `Finished fetching evaluations for ${programa}. Total found: ${evaluaciones.length}`
  );
  return evaluaciones;
}

export async function sync_evaluaciones() {
  const programas = await list_programas();
  if (!programas || programas.length === 0) {
    log("No programs found to process.");
    return;
  }
  log(`Got ${programas.length} programs. Starting sync process...`);
  await get_and_sync_evaluaciones(programas);
  log("Sync process complete.");
}

// --- Main execution block ---
// Use async IIFE (Immediately Invoked Function Expression) for top-level await
(async () => {
  // Check if the script is being run directly
  // Note: process.argv[1] might not be reliable in all execution environments (like ESM loaders)
  // A more robust check might be needed depending on how you run the script.
  // This check is generally okay for simple `node evaluacion.js` calls.
  const mainScriptPath = (await import("url")).fileURLToPath(import.meta.url);
  if (process.argv[1] === mainScriptPath) {
    log("Running evaluations sync directly...");
    try {
      await sync_evaluaciones();
      log("Exiting script successfully.");
      process.exit(0); // Explicit success exit code
    } catch (error) {
      logError("Unhandled error during script execution:", error);
      process.exit(1); // Explicit error exit code
    }
  }
})(); // Execute the async IIFE
