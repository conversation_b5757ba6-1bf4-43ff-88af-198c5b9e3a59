import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_date_string } from "./primitives.js";
import knex from "knex";

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const PERIODOS_API_URL = "https://api.q10.com/v1/periodos";
const INSCRIPTOS_API_URL = "https://api.q10.com/v1/inscripciones";
const LIMIT = 400;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 0;
let moreData = true;

async function sync_inscripcion(inscripcion) {
  log(
    `Consecutivo_inscripcion: ${inscripcion.Consecutivo_inscripcion} - Codigo_estudiante: ${inscripcion.Codigo_estudiante} - Nombre_completo: ${inscripcion.Nombre_completo}`
  );

  await knex_conn("Inscripciones")
    .select()
    .where("Consecutivo_inscripcion", inscripcion.Consecutivo_inscripcion)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Inscripciones").insert({
          ...inscripcion,
        });
      } else {
        await knex_conn("Inscripciones")
          .update({
            ...inscripcion,
          })
          .where(
            "Consecutivo_inscripcion",
            inscripcion.Consecutivo_inscripcion
          );
      }
    });
}

export async function list_periodos() {
  OFFSET = 1;
  let periodos = [];
  try {
    let response = undefined;
    while (true) {
      try {
        // Fetch all courses
        response = await axios.get(PERIODOS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      for (const periodo of response.data) {
        periodos.push(Number(periodo.Consecutivo));
      }

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  }

  return periodos;
}

export async function get_and_sync_inscripciones(periodo) {
  OFFSET = 1;
  try {
    let response = undefined;
    while (true) {
      try {
        const req_url =
          INSCRIPTOS_API_URL + `?Consecutivo_periodo=${Number(periodo)}`;
        log(req_url);
        response = await axios.get(req_url, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error.response?.data);
        break;
      }
      if (response?.status != 200) {
        log(`Periodo: ${periodo} did not respond with a 200.`);
        log(response);
        break;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }

      const inscripciones = await clean_inscripciones(response.data);
      await sync_inscripciones_chunk(inscripciones);
    }
  } catch (error) {
    logError("Error:", error);
  }
}

export async function sync_inscripciones_chunk(inscripciones) {
  const chunkSize = 10;
  let inscripcionesChunk = [];
  for (let i = 0; i < inscripciones.length; i += chunkSize) {
    inscripcionesChunk = inscripciones.slice(i, i + chunkSize);

    let futuros = [];
    for (const inscripcion of inscripcionesChunk) {
      futuros.push(sync_inscripcion(inscripcion));
      // await sync_inscripcion(inscripcion);
      // console.log(inscripcion.Consecutivo_inscripcion);
    }

    let results = undefined;
    try {
      results = await Promise.allSettled(futuros);
    } catch (error) {
      log("At least 1 inscripcion failed to sync.");
    }
    // Record the ones that failed.
    const failed_inscripciones = results.filter((p) => p.status === "rejected");
    for (const failed_inscripcion of failed_inscripciones) {
      // const failed_inscripcion = failed_inscripciones[i];
      log(`FAILED inscripcion: ${failed_inscripcion?.reason}`);
    }
  }
}

export async function clean_inscripciones(raw_inscripciones) {
  // Clean
  let inscripciones = [];
  for (const inscripcion of raw_inscripciones) {
    inscripcion.Nombre_periodo = inscripcion.per_nombre;
    delete inscripcion.per_nombre;

    inscripciones.push(structuredClone(inscripcion));
  }
  return inscripciones;
}

export async function sync_inscripciones() {
  const periodos = await list_periodos();
  for (const periodo of periodos) {
    log(`------------ Periodo: ${periodo} ------------`);
    await get_and_sync_inscripciones(periodo);
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_inscripciones();
  process.exit();
}
