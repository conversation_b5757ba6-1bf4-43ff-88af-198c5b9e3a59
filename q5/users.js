import axios from "axios";
import { config, usr, psw } from "./config.js";
import { get_token, close_connection } from "./primitives.js";
import { fix_user, list_pageof_users, add_user } from "./utils_users.js";

export async function sync_users(usr, psw) {
  let token = await get_token(usr, psw);

  let users = undefined;
  let next_page = undefined;
  let futuros = [];
  let added_user = undefined;
  while (true) {
    [users, next_page] = await list_pageof_users(token, next_page);
    for (const i in users) {
      const user = users[i];
      console.log(`${user.first_name} ${user.last_name} -- ${user.id}`);

      try {
        added_user = add_user(user);
      } catch (error) {
        console.log(
          `Could not queue user: ${user.first_name} ${user.last_name} -- ${user.id}`
        );
        throw error;
      }
      futuros.push(added_user);
    }
    try {
      // Block until all contacts are done.
      await Promise.all(futuros);
    } catch (error) {
      console.log("At least 1 of the previous ~25 users failed to sync.");
      throw error;
    }
    futuros = [];
    console.log(next_page);
    if (next_page == null) {
      break;
    }
  }
  console.log(" --- done syncing users--- ");
  return token;
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  const token = await sync_users(usr, psw);
  await close_connection(token);
  process.exit();
}
