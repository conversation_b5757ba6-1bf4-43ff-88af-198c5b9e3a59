import axios from "axios";
import { config, q10_api_key } from "./config.js";
import async from "async";
import knex from "knex";

const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const PAGOSCREDITOS_API_URL = "https://api.q10.com/v1/pagos/ordenespago";
const LIMIT = 400;
let OFFSET = 1;

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

export async function add_formas_detalles_pagos_ordenes_de_pago(pagoData) {
  await knex_conn.transaction(async (trx) => {
    const formas_pago = pagoData.Formas_pago;
    const detalles = pagoData.Detalles;
    delete pagoData.Formas_pago;
    delete pagoData.Detalles;

    await trx.insert(pagoData).into("Pagos_ordenes_pago");

    await Promise.all(
      formas_pago.map((formaPago) =>
        trx
          .insert({
            ...formaPago,
            Consecutivo_pago: pagoData.Consecutivo_pago,
          })
          .into("Formas_pago_ordenes_pago")
      )
    );
    await Promise.all(
      detalles.map((detalle_item) =>
        trx
          .insert({
            ...detalle_item,
            Consecutivo_pago: pagoData.Consecutivo_pago,
          })
          .into("Detalles_pagos_ordenes_pago")
      )
    );
  });
}

export async function get_pagos_ordenes_de_pago() {
  let pagos = [];
  OFFSET = 1;
  const req_link = PAGOSCREDITOS_API_URL;

  log(req_link);
  const instance = axios.create({
    baseURL: req_link,
    timeout: 50000,
    headers: { "API-Key": q10_api_key },
  });

  try {
    let response = undefined;
    const newDate = new Date();
    newDate.setFullYear(newDate.getFullYear() - 1);
    const today = new Date();
    const initialDateIso = newDate.toISOString().split("T")[0];
    const endDateIso = today.toISOString().split("T")[0];
    while (true) {
      try {
        response = await instance.get(req_link, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
            Fecha_inicio: initialDateIso,
            Fecha_fin: endDateIso,
          },
        });
      } catch (error) {
        logError(error);
        break;
      }
      if (response?.status != 200) {
        break;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      pagos.push(...structuredClone(response.data));

      // This API always says it has 0 items, so I'll be breaking after the frist page:
      // if (n_items == 0) {
      //   break;
      // } else {
      //   OFFSET += 1;
      // }

      if (pages == actualPage) {
        break;
      } else {
        OFFSET += 1;
      }
    }
  } catch (error) {
    logError(error);
  }
  return pagos;
}

export async function sync_pagos_ordenes_de_pago() {
  // Fetch data from the API for each period
  const pagos_ordenes_de_pago = await get_pagos_ordenes_de_pago();

  for (const pagoData of pagos_ordenes_de_pago) {
    const existingPago = await knex_conn
      .select("Consecutivo_pago")
      .from("Pagos_ordenes_pago")
      .where("Consecutivo_pago", pagoData.Consecutivo_pago)
      .first();

    if (!existingPago) {
      log(
        `Pago ${pagoData.Consecutivo_pago}, numero recibo pago: ${pagoData.Numero_recibo_pago} no existe, agregando`
      );
      await add_formas_detalles_pagos_ordenes_de_pago(pagoData);
    } else {
      log(
        `Pago ${pagoData.Consecutivo_pago}, numero recibo pago: ${pagoData.Numero_recibo_pago} ya existente.`
      );
    }
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_pagos_ordenes_de_pago();
  process.exit();
}
