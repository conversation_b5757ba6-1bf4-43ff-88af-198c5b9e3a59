import axios from "axios";
import { config, q10_api_key } from "./config.js";
import {
  add_credito_ordenDePago_cuota,
  get_ordenes_de_pago_from_person,
} from "./utils_creditos.js";
import knex from "knex";
import { get_estudiante, sync_estudiante } from "./estudiantes.js";
import { log, logError } from "../utils/logging.js";

const knex_conn = knex({
  client: "mssql",
  connection: config,
});
// graph TD

// Creditos -->  Periodos
// Creditos -->|Consulta \n por estados| Creditos["/creditos"]
// Creditos --> Cuotas
// Creditos --> Ordenes_Pago

// Ordenes_de_Pago["/ordenespago"] -->|Consulta\n por Fechas| Ordenes_Pago

// Pagos["/pagos/creditos"] --> Forma_Pago[Formas\n de pago]
// Pagos --> Detalle_Cuotas[Detalle \n de cuotas]

// API URL
const CREDITOS_API_URL = "https://api.q10.com/v1/creditos";
const LIMIT = 800;
let OFFSET = 1;
let moreData = true;

export async function get_periodos() {
  return await knex_conn("periods").select("Consecutivo");
}

export async function sync_creditos() {
  let periods = await get_periodos();
  periods.reverse();
  log(`Got ${periods.length} periods.`);
  try {
    for (const period of periods) {
      let response = undefined;
      // if (Number(period.Consecutivo) != 1093) {
      //   //This is for only doing certain period
      //   continue;
      // }
      moreData = true;
      while (moreData) {
        //Get that at least once, if more data is avail, do it again
        moreData = false;
        try {
          response = await axios.get(CREDITOS_API_URL, {
            params: {
              Limit: LIMIT,
              Offset: OFFSET,
              Consecutivo_periodo: period.Consecutivo,
            },
            headers: {
              "API-Key": q10_api_key,
            },
          });
        } catch (error) {
          log(`Periodo #${period.Consecutivo} no tiene creditos: `);
          log(error);
          continue;
        }
        if (response?.status != 200) {
          continue;
        }
        const pages = response.headers["x-paging-pagecount"];
        const totalResults = response.headers["x-paging-totalitemcount"];
        const actualPage = response.headers["x-paging-x-paging-pagenumber"];
        if (pages > actualPage && pages != 0) {
          moreData = true;
        }

        log(
          `----------- Periodo: #${period.Consecutivo} - con ${totalResults} créditos -- Procesando los primeros ${LIMIT} -----------`
        );

        await sync_creditos_pag(response.data);
      }
    }

    log("Data stored in the database.");
  } catch (error) {
    logError("Error syncing creditos:", error);
  } finally {
    // Destroy the connection pool
    await knex_conn.destroy();
  }
}

export function filter_ordenes_pagos(
  all_ordenes_pagos,
  ordenes_pagos_from_credits
) {
  let numeros_ordenes_pagos = [];
  for (const orden_de_pago of ordenes_pagos_from_credits) {
    numeros_ordenes_pagos.push(orden_de_pago.Numero_orden_pago);
  }

  let ordenes_pagos_with_credits = [];
  for (const full_orden_de_pago of all_ordenes_pagos) {
    const idx = numeros_ordenes_pagos.indexOf(
      full_orden_de_pago.Numero_orden_pago
    );
    if (idx != -1) {
      full_orden_de_pago.Valor_total =
        ordenes_pagos_from_credits[idx].Valor_total;
      full_orden_de_pago.Valor_en_este_credito =
        ordenes_pagos_from_credits[idx].Valor_en_este_credito;

      ordenes_pagos_with_credits.push(structuredClone(full_orden_de_pago));
    }
  }
  return ordenes_pagos_with_credits;
}

export async function sync_creditos_pag(data) {
  for (const credit of data) {
    try {
      if (!credit.Codigo_programa) {
        continue;
      }
      
      log(` - credit: ${credit.Consecutivo_credito}`);
      
      const all_ordenes_pagos = await get_ordenes_de_pago_from_person(
        credit.Codigo_estudiante
      );

      const ordenes_pagos = filter_ordenes_pagos(
        all_ordenes_pagos,
        credit.Ordenes_pago
      );

      log(`Got ordenes de pago for ${credit.Codigo_estudiante}`);
      
      let existingCredito = null;
      let existingEstudiante = null;
      
      try {
        existingCredito = await knex_conn("creditos")
          .select(["Estado_credito"])
          .where("Consecutivo_credito", credit.Consecutivo_credito)
          .first();
      } catch (dbError) {
        logError(`DB error checking existing credito ${credit.Consecutivo_credito}:`, dbError);
      }
      
      try {
        existingEstudiante = await knex_conn("Estudiante")
          .select(["Codigo_estudiante"])
          .where("Codigo_estudiante", credit.Codigo_estudiante)
          .first();
      } catch (dbError) {
        logError(`DB error checking existing estudiante ${credit.Codigo_estudiante}:`, dbError);
      }

      if(!existingEstudiante){
        log(`Estudiante ${credit.Codigo_estudiante} doesn't exist, creating`);
        try {
          const estudiante = await get_estudiante(credit.Codigo_estudiante);
          if(estudiante){
            await sync_estudiante(estudiante);
          }
        } catch (estudianteError) {
          logError(`Error creating estudiante ${credit.Codigo_estudiante}:`, estudianteError);
        }
      }
      
      log(`Start transaction for Credit # ${credit.Consecutivo_credito}`);
      await knex_conn.transaction(async (trx) => {
        try {
          const cuotas = credit.Cuotas;
          delete credit.Cuotas;
          delete credit.Ordenes_pago;
          delete credit.Pagos_pendientes;
          delete credit.Facturas;

          await add_credito_ordenDePago_cuota(
            trx,
            credit,
            ordenes_pagos,
            cuotas,
            existingCredito
          );

          log(`Committed ! ${credit.Consecutivo_credito}`);
          await trx.commit();
        } catch (error) {
          trx.rollback();
          logError(`Transaction error for credit ${credit.Consecutivo_credito}:`, error);
        }
      });
    } catch (creditError) {
      logError(`Error processing credit ${credit?.Consecutivo_credito || 'unknown'}:`, creditError);
    }
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  // Call the function to start fetching and storing data
  await sync_creditos();
  process.exit();
}
