import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_datetime_string } from "./primitives.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});
import async from "async";

// API URL
const CURSOS_API_URL = "https://api.q10.com/v1/cursos";
const PROGRAMAS_API_URL = "https://api.q10.com/v1/programas";
const EVALUACIONES_API_URL = "https://api.q10.com/v1/evaluaciones";

const LIMIT = 200;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

async function sync_evaluacion(evaluacion) {
  // Some evaluaciones don't come from actual cursos
  const curso = await get_curso_details(
    evaluacion.Consecutivo_curso,
    evaluacion.Consecutivo_periodo
  );

  const last_update = to_cr_datetime_string(new Date());

  log(`${evaluacion.Codigo_estudiante} -- ${curso.Consecutivo}`);

  await knex_conn("Evaluacion")
    .select()
    .where("id_estudiante", evaluacion.Codigo_estudiante)
    .where("Codigo_asignatura", evaluacion.Codigo_asignatura)
    .where("Consecutivo_periodo", curso.Consecutivo_periodo)
    .where("Consecutivo_pensum", evaluacion.Consecutivo_pensum)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Evaluacion").insert({
          id_estudiante: evaluacion.Codigo_estudiante,
          Consecutivo_curso: curso.Consecutivo,
          Programa_codigo: evaluacion.Codigo_programa,
          Consecutivo_periodo: curso.Consecutivo_periodo,
          Consecutivo_sede_jornada: curso?.Consecutivo_sede_jornada,
          Codigo_curso: evaluacion.Codigo_curso,
          Nombre_completo: evaluacion.Nombre_completo,
          Nombre_asignatura: evaluacion.Nombre_asignatura,
          Nombre_periodo: evaluacion.Nombre_periodo,
          Nombre_programa: evaluacion.Nombre_programa,
          Nombre_curso: evaluacion.Nombre_curso,
          Codigo_matricula: evaluacion.Codigo_matricula,
          Estado_matricula_asignatura: evaluacion.Estado_matricula_asignatura,
          Promedio_evaluacion: evaluacion.Promedio_evaluacion,
          Porcentaje_evaluado: evaluacion.Porcentaje_evaluado,
          Porcentaje_inasistencia: evaluacion.Porcentaje_inasistencia,
          Cantidad_inasistencia: evaluacion.Cantidad_inasistencia,
          Promedio_acumulado: evaluacion.Promedio_acumulado,
          Promedio_periodo: evaluacion.Promedio_periodo,
          Consecutivo_pensum: evaluacion.Consecutivo_pensum,
          Nombre_pensum: evaluacion.Nombre_pensum,
          Ultima_actualizacion: last_update,
          Codigo_asignatura: evaluacion.Codigo_asignatura,
          Numero_identificacion: evaluacion.Numero_identificacion,
        });
      } else {
        await knex_conn("Evaluacion")
          .update({
            Programa_codigo: evaluacion.Codigo_programa,
            Consecutivo_sede_jornada: curso?.Consecutivo_sede_jornada,
            Codigo_curso: evaluacion.Codigo_curso,
            Nombre_completo: evaluacion.Nombre_completo,
            Nombre_asignatura: evaluacion.Nombre_asignatura,
            Nombre_periodo: evaluacion.Nombre_periodo,
            Nombre_programa: evaluacion.Nombre_programa,
            Nombre_curso: evaluacion.Nombre_curso,
            Codigo_matricula: evaluacion.Codigo_matricula,
            Estado_matricula_asignatura: evaluacion.Estado_matricula_asignatura,
            Promedio_evaluacion: evaluacion.Promedio_evaluacion,
            Porcentaje_evaluado: evaluacion.Porcentaje_evaluado,
            Porcentaje_inasistencia: evaluacion.Porcentaje_inasistencia,
            Cantidad_inasistencia: evaluacion.Cantidad_inasistencia,
            Promedio_acumulado: evaluacion.Promedio_acumulado,
            Promedio_periodo: evaluacion.Promedio_periodo,
            Consecutivo_pensum: evaluacion.Consecutivo_pensum,
            Nombre_pensum: evaluacion.Nombre_pensum,
            Ultima_actualizacion: last_update,
            Codigo_asignatura: evaluacion.Codigo_asignatura,
            Numero_identificacion: evaluacion.Numero_identificacion,
          })
          .where("id_estudiante", evaluacion.Codigo_estudiante)
          .where("Codigo_asignatura", evaluacion.Codigo_asignatura)
          .where("Consecutivo_periodo", curso.Consecutivo_periodo)
          .where("Consecutivo_pensum", evaluacion.Consecutivo_pensum);
      }
    });
}

export async function get_curso_details(
  Consecutivo_curso,
  Consecutivo_periodo
) {
  OFFSET = 0;
  // In case the curso doesn't exist, at least return an empty curso
  let curso = {};
  // mandatory fields for any curso:
  curso.Consecutivo = Consecutivo_curso;
  curso.Consecutivo_periodo = Consecutivo_periodo;
  try {
    let response = undefined;

    try {
      // Fetch all courses
      response = await axios.get(CURSOS_API_URL + `/${Consecutivo_curso}`, {
        params: {
          Limit: LIMIT,
          Offset: OFFSET,
        },
        headers: {
          "API-Key": q10_api_key,
        },
      });
    } catch (error) {
      logError(
        `Warning. Failed link. Couldn't get Consecutivo_curso: ${Consecutivo_curso}`
      );
    }
    if (response?.status === 200) {
      curso = response.data;
    }
  } catch (error) {
    logError(
      `Warning. Couldn't get Consecutivo_curso: ${Consecutivo_curso}`
    );
  }

  return curso;
}

export async function list_programas() {
  OFFSET = 1;
  let programas = [];
  try {
    let response = undefined;
    while (true) {
      try {
        // Fetch all courses
        response = await axios.get(PROGRAMAS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        log(error);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }
      programas.push(...structuredClone(response.data));

      if (pages == actualPage) {
        break;
      }
    }
  } catch (error) {
    logError("Error:", error);
  }

  return programas;
}

export async function get_and_sync_evaluaciones(programas) {
  try {
    for (const j in programas) {
      const programa = programas[j];
      log(`${programa}`);

      let evaluaciones = undefined;
      try {
        evaluaciones = await get_evaluaciones(programa);
      } catch (error) {
        log("ERROR. Could not get evaluaciones.");
        log(error);
        continue;
      }

      // Doing only 1 at the time, otherwise we hit the rate limit
      for (const evaluacion of evaluaciones) {
        await sync_evaluacion(evaluacion);
      }
      // await async.mapLimit(evaluaciones, 2, async (evaluacion) => {
      //   await sync_evaluacion(evaluacion);
      // });

      log("---");
    }
  } catch (error) {
    logError("Error:", error);
  }
}

export async function get_evaluaciones(programa) {
  let evaluaciones = [];
  OFFSET = 1;
  const req_link = EVALUACIONES_API_URL + `?Programa=${programa}`;

  log(req_link);
  const instance = axios.create({
    baseURL: req_link,
    timeout: 100000,
    headers: { "API-Key": q10_api_key },
  });

  try {
    let response = undefined;
    while (true) {
      try {
        response = await instance.get(req_link, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
        });
      } catch (error) {
        log(error.code);
        break;
      }
      if (response?.status != 200) {
        break;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      evaluaciones.push(...structuredClone(response.data));

      log(`Got ${evaluaciones.length} evaluaciones`);

      // This API always says it has 0 items, so I'll be breaking after the frist page:
      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }

      if (pages == actualPage) {
        break;
      } else {
        OFFSET += 1;
      }
    }
  } catch (error) {
    log(error);
  }
  return evaluaciones;
}

export async function sync_evaluaciones() {
  // const periodos = await list_periodos();
  const programas = [
    "EM09-TECN",
    "EM09-TECO",
    "EM02-TECI",
    "EM10-TECB",
    "EM10-TECN",
    "EM02-TECA",
    "EM02-TECE",
    "EA01-TECB",
    "CO01-TECA",
    "DE02-TECA",
    "DE01-TECA",
    "TI09-TECC",
    "PU01-TECA",
    "DECO",
    "TI02-TECB",
    "TU03-TECD",
    "ID04-TECC",
    "CO01-TECB",
    "CO01-TECD",
    "CO01-TECE",
    "CO01-TECC",
    "TI01-TECN",
    "EM01-TECU",
    "EM02-TECC",
    "EM09-TECF",
    "EM09-TECT",
    "EM09-TECU",
    "TU01-TECB",
    "EM13-TECJ",
    "ID08-TECA",
    "ID01-TECA",
    "TI09-TECB",
    "CO04TECF",
    "CO01-TECJ",
    "TI01-TECA",
    "CO01-TECK",
    "CO04-TECE",
    "CO01-TECL",
    "EM09-TECD",
    "EM09-TECP",
    "EM09-TECQ",
    "EA01-TECC",
    "EM03-TECC",
    "EM03-TECF",
    "EM03-TECH",
    "EM03-TECD",
    "EM03-TECG",
    "EM10-TECD",
    "EM10-TECP",
    "SE09-TECC",
    "SE09-TECI",
    "SE09-TECJ",
    "SE10-TECC",
    "SE10-TECI",
    "SE01-TECL",
    "SE01-TECQ",
    "SE01-TECK",
    "SE01-TECM",
    "TI01-TECB",
    "TI07-TECA",
    "EM01-TECF",
    "SE10-TECB",
    "SE10-TECG",
    "SE09-TECB",
    "SE02-TECF",
    "SE02-TECA",
    "SE02-TECH",
    "SE02-TECE",
    "SE09-TECG",
    "SE09-TECH",
    "SE02-TECG",
    "SE02-TECB",
    "EM01-TECE",
    "EM09-TECE",
    "EM01-TECP",
    "EM01-TECS",
    "EM01-TECO",
    "EM09-TECR",
    "EM09-TECS",
    "EM10-TECE",
    "EM10-TECR",
    "TI01-TECC",
    "SO01-TECA",
    "SO02-TECA",
    "SO01-TECC",
    "SO01-TECB",
    "SO02-TECB",
    "SO02-TECC",
    "SO03-TECA",
    "SO03-TECB",
    "EM04-TECA",
    "SE09-TECA",
    "SE01-TECJ",
    "SE01-TECC",
    "SE01-TECO",
    "SE01-TECI",
    "SE09-TECE",
    "SE09-TECF",
    "SE01-TECN",
    "SE10-TECA",
    "SE10-TECE",
    "EA01-TECD",
    "TI01-TECM",
    "TI03-TECA",
    "EM03-TECB",
    "TU01-TECA",
    "TI08-TECC",
    "TI08-TECA",
    "TI08-TECB",
    "CO01-TECF",
    "TI01-TECH",
    "SE01-TECH",
    "ID01-TECK",
    "SE03-TECC",
    "SE03-TECB",
    "TI06-TECB",
    "TI04-TECB",
    "EM01-TECJ",
    "EM06-TECC",
    "EM01-TECL",
    "SE01-TESC",
    "EM01-TECK",
    "SE01-TECG",
    "ID04-TECB",
    "EM02-TECF",
    "EM06-TECE",
    "EM06-TECA",
    "DE05-TECD",
    "DE05-TECA",
    "PU03-TECA",
    "PU02-TECA",
    "PU03-TECB",
    "DE05-TECC",
    "DE01-TECC",
    "DE01-TECD",
    "DE03-TECA",
    "DE05-TECB",
    "SE01-TESE",
    "LI01-TECA",
    "CO04-TECB",
    "CO02-TECA",
    "DE01-TECE",
    "SE01-TESB",
    "SE01-TECF",
    "SE01-TESH",
    "ID01-TECM",
    "SE01-TESD",
    "ID04-TECA",
    "TI04-TECA",
    "EM12-TECA",
    "EM06-TECB",
    "EM01-TECI",
    "TI04-TECC",
    "TI06-TECD",
    "EM06-TECD",
    "SE03-TECA",
    "SE01-TESG",
    "SE01-TESA",
    "TI06-TECA",
    "TI06-TECC",
    "ID07-TECA",
  ];

  log(`Got ${programas.length} programas`);

  await get_and_sync_evaluaciones(programas);
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_evaluaciones();
  process.exit();
}
