// const axios = require("axios");
import axios from "axios";
import { config, q10_api_key } from "./config.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});

const API_URL = "https://api.q10.com/v1/impuestos?Estado=Activo";
const LIMIT = 400;
let OFFSET = 0;

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

export async function sync_impuestos() {
  let response;
  try {
    response = await axios.get(API_URL, {
      params: { Limit: LIMIT, Offset: OFFSET },
      headers: {
        "API-Key": q10_api_key,
      },
    });
  } catch (error) {
    logError("Error fetching data from API :", error);
    await knex_conn.destroy();
  }
  const data = response.data;

  if (data.length === 0) {
    log("No more data to fetch.");
    await knex_conn.destroy();
    return;
  }

  const impuestosData = [];
  for (const impuesto of data) {
    let existingimpuesto;
    try {
      existingimpuesto = await knex_conn("Impuestos")
        .select("Consecutivo_impuesto")
        .where("Consecutivo_impuesto", impuesto.Consecutivo_impuesto)
        .first();
    } catch (error) {
      logError("Error checking for impuestos :", error);
      await knex_conn.destroy();
    }
    if (!existingimpuesto) {
      impuestosData.push({
        Consecutivo_impuesto: impuesto.Consecutivo_impuesto,
        Codigo_impuesto: impuesto.Codigo_impuesto,
        Nombre: impuesto.Nombre,
        Porcentaje: impuesto.Porcentaje,
        Codigo_tipo_impuesto: impuesto.Codigo_tipo_impuesto,
        Nombre_tipo_impuesto: impuesto.Nombre_tipo_impuesto,
        Estado: impuesto.Estado,
      });
    }
  }
  if (impuestosData.length > 0) {
    await knex_conn.transaction(async (trx) => {
      await trx("Impuestos").insert(impuestosData);
    });
  }

  log(`Fetched and stored ${data.length} records.`);
  OFFSET += LIMIT;

  if (data.length === LIMIT) {
    sync_impuestos();
  }
  return;
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  try {
    await sync_impuestos();
  } catch (error) {
    logError("Error creating table or fetching data:", error);
    await knex_conn.destroy();
  }
  process.exit();
}
