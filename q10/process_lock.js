import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const LOCK_FILE = path.join(__dirname, 'sync.lock');

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(message) {
  console.log(`${getISODateTime()} ${message}`);
}

export function acquireLock() {
  try {
    if (fs.existsSync(LOCK_FILE)) {
      const lockData = JSON.parse(fs.readFileSync(LOCK_FILE, 'utf8'));
      const { pid, timestamp } = lockData;
      const lockTime = new Date(timestamp);
      const now = new Date();
      const hoursSinceLock = (now - lockTime) / (1000 * 60 * 60);

      try {
        process.kill(pid, 0);

        if (hoursSinceLock > 48) {
          log('KILLING PREVIOUS INSTANCE');
          process.kill(pid);
        } else {
          log('Another sync process is already running');
          return false;
        }
      } catch (e) {
        // Process does not exist, lock is stale
        log('Found stale lock for a non-existent process, proceeding with sync');
      }
    }

    fs.writeFileSync(LOCK_FILE, JSON.stringify({
      pid: process.pid,
      timestamp: new Date().toISOString()
    }));
    return true;
  } catch (error) {
    log('Error acquiring lock:', error);
    return false;
  }
}

export function releaseLock() {
  try {
    if (fs.existsSync(LOCK_FILE)) {
      fs.unlinkSync(LOCK_FILE);
    }
  } catch (error) {
    log('Error releasing lock:', error);
  }
} 