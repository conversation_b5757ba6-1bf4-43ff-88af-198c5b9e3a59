import axios from "axios";
import knex from "knex";
import { config, usr, psw } from "./config.js";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});
import { to_cr_time_string, to_date_string } from "./primitives.js";
import { get_contact } from "./utils_contactos.js";

export function fix_user(user) {
  let fixed_user = user;

  // if (task.task_type != null) {
  //   try {
  //     fixed_task.task_type = Number(fixed_task.task_type.split("/").at(-2));
  //   } catch (error) {
  //     try {
  //       fixed_task.task_type = Number(fixed_task.task_type);
  //     } catch (error2) {
  //       console.log(
  //         `Could not get the task type for task: ${JSON.stringify(task)}`
  //       );
  //       throw error;
  //     }
  //   }
  // }

  // if (task.created != null) {
  //   fixed_task.created = to_cr_time_string(task.created);
  // }

  return fixed_user;
}

export async function list_pageof_users(
  api_key,
  USERS_API_URL = `https://api.clientify.net/v1/users?page_size=200`
) {
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  try {
    response = await axios.get(USERS_API_URL, { headers: header });
  } catch (error) {
    console.log(error);
    throw error;
  }

  if (response?.status == 200) {
    return [response.data.results, response.data.next];
  } else {
    console.log(response);
    return "";
  }
}

export async function add_user(user) {
  await knex_conn("Usuarios")
    .select()
    .where("id", user.id)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Usuarios").insert({
          id: user.id,
          Username: user.username,
          Nombres: user.first_name,
          Apellidos: user.last_name,
          Pais: user.country,
          Telefono: user.phone,
          Compania: user.company,
          Es_admin: user.is_company_admin,
          Usuario_inbox: user.inbox_user,
          Es_tester: user.is_tester,
        });
      } else {
        await knex_conn("Usuarios")
          .update({
            Username: user.username,
            Nombres: user.first_name,
            Apellidos: user.last_name,
            Pais: user.country,
            Telefono: user.phone,
            Compania: user.company,
            Es_admin: user.is_company_admin,
            Usuario_inbox: user.inbox_user,
            Es_tester: user.is_tester,
          })
          .where("id", user.id);
      }
    });
}
