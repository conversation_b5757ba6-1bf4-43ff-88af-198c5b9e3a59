import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file path and directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PAGE_TRACKER_FILE = path.join(__dirname, 'last_page.json');

export function saveLastPage(pageNumber) {
  const data = {
    pageNumber,
    timestamp: new Date().toISOString(),
  };
  fs.writeFileSync(PAGE_TRACKER_FILE, JSON.stringify(data));
}

export function getLastPage() {
  if (!fs.existsSync(PAGE_TRACKER_FILE)) {
    return { pageNumber: 0, timestamp: null };
  }

  const data = JSON.parse(fs.readFileSync(PAGE_TRACKER_FILE, 'utf-8'));
  return data;
}

export function isPageDataRecent(timestamp) {
  if (!timestamp) return false;
  const now = new Date();
  const lastSaved = new Date(timestamp);
  const diffInHours = (now - lastSaved) / (1000 * 60 * 60);
  return diffInHours <= 48;
} 