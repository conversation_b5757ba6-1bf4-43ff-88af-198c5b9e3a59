import axios from "axios";
import { config, q10_api_key } from "./config.js";
import knex from "knex";

function getISODateTime() {
  const now = new Date();
  return now.toISOString().replace('T', ' ').split('.')[0] + '.' +
         now.getMilliseconds().toString().padStart(3, '0');
}

function log(...args) {
  console.log(getISODateTime(), ...args);
}

function logError(...args) {
  console.error(getISODateTime(), ...args);
}

const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const INASISTENCIAS_DE_PAGO_API_URL = "https://api.q10.com/v1/inasistencias";
const LIMIT = 400;
let OFFSET = 0;
let moreData = true;

export async function sync_inasistencia(inasistencia) {
  await knex_conn("Inasistencia")
    .select()
    .where(
      "Numero_identificacion_estudiante",
      inasistencia.Numero_identificacion_estudiante
    )
    .where("Inasistencia_id", inasistencia.Inasistencia_id)
    .then(async (rows) => {
      if (rows.length === 0) {
        await knex_conn("Inasistencia").insert({
          Inasistencia_id: inasistencia.Inasistencia_id,
          Primer_nombre: inasistencia.Primer_nombre,
          Segundo_nombre: inasistencia.Segundo_nombre,
          Primer_apellido: inasistencia.Primer_apellido,
          Segundo_apellido: inasistencia.Segundo_apellido,
          Numero_identificacion_estudiante:
            inasistencia.Numero_identificacion_estudiante,
          Curso_Codigo_modulo: inasistencia.Curso_Codigo_modulo,
          Curso_Nombre_modulo: inasistencia.Curso_Nombre_modulo,
          Curso_Codigo_curso: inasistencia.Curso_Codigo_curso,
          Curso_Nombre_curso: inasistencia.Curso_Nombre_curso,
          Curso_Numero_identificacion_docente:
            inasistencia.Curso_Numero_identificacion_docente,
          Curso_Nombre_docente: inasistencia.Curso_Nombre_docente,
          Curso_Horario_curso: inasistencia.Curso_Horario_curso,
          Curso_Periodo_curso: inasistencia.Curso_Periodo_curso,
          Curso_Fecha_inicio: inasistencia.Curso_Fecha_inicio,
          Curso_Fecha_fin: inasistencia.Curso_Fecha_fin,
          Curso_Cantidad_inasistencia: inasistencia.Curso_Cantidad_inasistencia,
          Curso_Observaciones: inasistencia.Curso_Observaciones,
          Inasistencia_Dia: inasistencia.Inasistencia_Dia,
          Inasistencia_Fecha: inasistencia.Inasistencia_Fecha,
          Inasistencia_Hora: inasistencia.Inasistencia_Hora,
          Inasistencia_Justificacion: inasistencia.Inasistencia_Justificacion,
          Inasistencia_Detalle_justificacion:
            inasistencia.Inasistencia_Detalle_justificacion,
        });
      } else {
        await knex_conn("Inasistencia")
          .update({
            Primer_nombre: inasistencia.Primer_nombre,
            Segundo_nombre: inasistencia.Segundo_nombre,
            Primer_apellido: inasistencia.Primer_apellido,
            Segundo_apellido: inasistencia.Segundo_apellido,
            curso_Codigo_modulo: inasistencia.curso_Codigo_modulo,
            curso_Nombre_modulo: inasistencia.curso_Nombre_modulo,
            curso_Codigo_curso: inasistencia.curso_Codigo_curso,
            curso_Nombre_curso: inasistencia.curso_Nombre_curso,
            curso_Numero_identificacion_docente:
              inasistencia.curso_Numero_identificacion_docente,
            curso_Nombre_docente: inasistencia.curso_Nombre_docente,
            curso_Horario_curso: inasistencia.curso_Horario_curso,
            curso_Periodo_curso: inasistencia.curso_Periodo_curso,
            curso_Fecha_inicio: inasistencia.curso_Fecha_inicio,
            curso_Fecha_fin: inasistencia.curso_Fecha_fin,
            curso_Cantidad_inasistencia:
              inasistencia.curso_Cantidad_inasistencia,
            curso_Observaciones: inasistencia.curso_Observaciones,
            inasistencia_Dia: inasistencia.inasistencia_Dia,
            inasistencia_Hora: inasistencia.inasistencia_Hora,
            inasistencia_Justificacion: inasistencia.inasistencia_Justificacion,
            inasistencia_Detalle_justificacion:
              inasistencia.inasistencia_Detalle_justificacion,
          })
          .where(
            "Numero_identificacion_estudiante",
            inasistencia.Numero_identificacion_estudiante
          )
          .where("Inasistencia_id", inasistencia.Inasistencia_id);
      }
    });
}

export async function sync_ordenes_de_pago() {
  // Fetch data from the API for each period
  let response = undefined;
  const newDate = new Date();
  newDate.setFullYear(newDate.getFullYear() - 1);
  const today = new Date();
  const initialDateIso = newDate.toISOString().split("T")[0];
  const endDateIso = today.toISOString().split("T")[0];
  moreData = true;
  while (moreData) {
    //Get that at least once, if more data is avail, do it again
    moreData = false;
    try {
      response = await axios.get(INASISTENCIAS_DE_PAGO_API_URL, {
        params: {
          Limit: LIMIT,
          Offset: OFFSET,
          Fecha_inicio_inasistencia: initialDateIso,
          Fecha_fin_inasistencia: endDateIso,
        },
        headers: {
          "API-Key": q10_api_key,
        },
      });
    } catch (error) {
      console.log("No hay inasistencias", error);
    }
    const pages = response?.headers["x-paging-pagecount"];
    const totalResults = response?.headers["x-paging-totalitemcount"];
    const actualPage = response?.headers["x-paging-pagenumber"];
    console.log(
      `Paginas: ${pages} - Resultados  ${totalResults} - Página Actual  ${actualPage}`
    );
    if (pages > actualPage && pages != 0) {
      moreData = true;
      OFFSET += 1;
    }
    console.log(`Total inasistencias ${totalResults}: Procesando ${LIMIT}`);
    await sync_inasistencias_page(response.data);
  }
}

export async function fix_inasistencia(
  student_data,
  curso,
  inasistencia,
  inasistencia_id
) {
  let clean_inasistencia = {};
  clean_inasistencia.Primer_nombre = student_data.Primer_nombre;
  clean_inasistencia.Segundo_nombre = student_data.Segundo_nombre;
  clean_inasistencia.Primer_apellido = student_data.Primer_apellido;
  clean_inasistencia.Segundo_apellido = student_data.Segundo_apellido;
  clean_inasistencia.Numero_identificacion_estudiante =
    student_data.Numero_identificacion_estudiante;
  clean_inasistencia.Curso_Codigo_modulo = curso.Codigo_modulo;
  clean_inasistencia.Curso_Nombre_modulo = curso.Nombre_modulo;
  clean_inasistencia.Curso_Codigo_curso = curso.Codigo_curso;
  clean_inasistencia.Curso_Nombre_curso = curso.Nombre_curso;
  clean_inasistencia.Curso_Numero_identificacion_docente =
    curso.Numero_identificacion_docente;
  clean_inasistencia.Curso_Nombre_docente = curso.Nombre_docente;
  clean_inasistencia.Curso_Horario_curso = curso.Horario_curso;
  clean_inasistencia.Curso_Periodo_curso = curso.Periodo_curso;
  clean_inasistencia.Curso_Fecha_inicio = curso.Fecha_inicio;
  clean_inasistencia.Curso_Fecha_fin = curso.Fecha_fin;
  clean_inasistencia.Curso_Cantidad_inasistencia = curso.Cantidad_inasistencia;
  clean_inasistencia.Curso_Observaciones = curso.Observaciones;
  clean_inasistencia.Inasistencia_Dia = inasistencia.Dia;
  clean_inasistencia.Inasistencia_Fecha = inasistencia.Fecha;
  clean_inasistencia.Inasistencia_Hora = inasistencia.Hora;
  clean_inasistencia.Inasistencia_Justificacion = inasistencia.Justificacion;
  clean_inasistencia.Inasistencia_Detalle_justificacion =
    inasistencia.Detalle_justificacion;

  clean_inasistencia.Inasistencia_id = inasistencia_id;

  return clean_inasistencia;
}
export async function sync_inasistencias_page(page_of_inasistencias) {
  for (const set_of_inasistencias of page_of_inasistencias) {
    let inasistencia_id = 1;
    let futuros = [];
    for (const curso of set_of_inasistencias.Cursos) {
      for (const inasistencia of curso.Inasistencias) {
        const clean_inasistencia = await fix_inasistencia(
          set_of_inasistencias,
          curso,
          inasistencia,
          inasistencia_id
        );
        futuros.push(sync_inasistencia(clean_inasistencia));
        inasistencia_id += 1;
      }
    }
    await Promise.allSettled(futuros).then((results) =>
      results.forEach((result) => {
        if (result.status != "fulfilled") {
          console.log(`FAILED inasistencia: ${result?.reason}`);
        }
      })
    );
    console.log(
      `Updated inasistencias from ${set_of_inasistencias.Primer_nombre} ${set_of_inasistencias.Primer_apellido} - ${set_of_inasistencias.Numero_identificacion_estudiante}`
    );
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_ordenes_de_pago();
  process.exit();
}
