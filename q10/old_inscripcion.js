import axios from "axios";
import { config, q10_api_key } from "./config.js";
import { to_cr_date_string } from "./primitives.js";
import knex from "knex";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});

// API URL
const CURSOS_API_URL = "https://api.q10.com/v1/cursos/";
const INSCRIPTOS_API_URL = "https://api.q10.com/v1/estudiantes";
const LIMIT = 800;
// this q10 API is 1-indexed for the offset, so it's not actually an offset.
let OFFSET = 1;
let moreData = true;

async function sync_inscripcion(inscripto, curso) {
  await knex_conn("Inscripcion")
    .select()
    .where("id_estudiante", inscripto.Codigo_estudiante)
    .where("id_curso", curso.Consecutivo)
    .then(async (rows) => {
      if (rows.length === 0) {
        console.log(
          `Estudiante ${inscripto.Codigo_estudiante} -- En el Curso ${curso.Consecutivo} no está inscripto`
        );
        await knex_conn("Inscripcion").insert({
          id_estudiante: inscripto.Codigo_estudiante,
          id_curso: curso.Consecutivo,
          Codigo_matricula: inscripto.Codigo_matricula,
          Fecha_matricula: to_cr_date_string(inscripto.Fecha_matricula),
          Fecha_renovacion: to_cr_date_string(inscripto.Fecha_renovacion),
          Condicion_matricula: inscripto.Condicion_matricula,
          Sede_nombre: inscripto.Nombre_sede,
          Jornada_nombre: inscripto.Nombre_jornada,
          Programa_nombre: inscripto.Nombre_programa,
          Grupo_nombre: inscripto.Nombre_grupo,
          Periodo_nombre: inscripto.Nombre_periodo,
          Nivel_nombre: inscripto.Nombre_nivel,
          Consecutivo_periodo: curso.Consecutivo_periodo,
          Consecutivo_sede_jornada: curso.Consecutivo_sede_jornada,
          Codigo_programa: curso.Codigo_programa,
        });
      } else {
        console.log(
          `Estudiante ${inscripto.Codigo_estudiante} -- En el Curso ${curso.Consecutivo} YA inscripto`
        );
        await knex_conn("Inscripcion")
          .update({
            Codigo_matricula: inscripto.Codigo_matricula,
            Fecha_matricula: to_cr_date_string(inscripto.Fecha_matricula),
            Fecha_renovacion: to_cr_date_string(inscripto.Fecha_renovacion),
            Condicion_matricula: inscripto.Condicion_matricula,
            Sede_nombre: inscripto.Nombre_sede,
            Jornada_nombre: inscripto.Nombre_jornada,
            Programa_nombre: inscripto.Nombre_programa,
            Grupo_nombre: inscripto.Nombre_grupo,
            Periodo_nombre: inscripto.Nombre_periodo,
            Nivel_nombre: inscripto.Nombre_nivel,
            Consecutivo_periodo: curso.Consecutivo_periodo,
            Consecutivo_sede_jornada: curso.Consecutivo_sede_jornada,
            Codigo_programa: curso.Codigo_programa,
          })
          .where("id_estudiante", inscripto.Codigo_estudiante)
          .where("id_curso", curso.Consecutivo);
      }
    });
}

async function get_estudiantes(curso) {
  OFFSET = 0;
  const search_url =
    INSCRIPTOS_API_URL +
    `?Periodo=${curso.Consecutivo_periodo}` +
    `&Limit=${LIMIT}` +
    `&Offset=${OFFSET}`;

  let response = undefined;
  try {
    response = await axios.get(search_url, {
      params: {
        Limit: LIMIT,
        Offset: OFFSET,
      },
      headers: {
        "API-Key": q10_api_key,
      },
    });
    if (response?.status == 200) {
      return response.data;
    }
  } catch (error) {
    if (error?.response?.data?.code === "404") {
      console.log(
        `${error?.response?.data?.message} Periodo= ${curso.Consecutivo_periodo} - Sede_jornada= ${curso.Consecutivo_sede_jornada} - id_curso= ${curso.Consecutivo} - Codigo_programa= ${curso.Codigo_programa}`
      );
    } else {
      console.log(`Failed link: ${search_url}`);
    }
  }
  return [];
}

export async function sync_inscripciones() {
  try {
    let response = undefined;
    while (true) {
      try {
        // Fetch all courses
        response = await axios.get(CURSOS_API_URL, {
          params: {
            Limit: LIMIT,
            Offset: OFFSET,
          },
          headers: {
            "API-Key": q10_api_key,
          },
        });
      } catch (error) {
        console.log(`Estudiante: ${id.Codigo_estudiante} no existe`);
        continue;
      }
      if (response?.status != 200) {
        continue;
      }
      const pages = response.headers["x-paging-pagecount"];
      const actualPage = response.headers["x-paging-pagenumber"];
      const n_items = response.headers["x-paging-totalitemcount"];
      console.log(
        `Total number of pages: ${pages}, items on this page ${n_items}, current page number: ${actualPage}`
      );

      if (n_items == 0) {
        break;
      } else {
        OFFSET += 1;
      }

      for (const curso of response.data) {
        console.log(`Periodo= ${curso.Consecutivo_periodo}`);
        const inscriptos = await get_estudiantes(curso);
        if (inscriptos.length === 0) {
          continue;
        }
        let futuros = [];
        for (const inscripto of inscriptos) {
          futuros.push(sync_inscripcion(inscripto, curso));
        }

        let results = undefined;
        try {
          results = await Promise.allSettled(futuros);
        } catch (error) {
          console.log("At least 1 of students of this course failed to sync.");
        }
        // Record the ones that failed.
        const failed_inscriptos = results.filter(
          (p) => p.status === "rejected"
        );
        for (const i in failed_inscriptos) {
          const failed_inscripto = failed_inscriptos[i];
          console.log(`FAILED INSCRIPTO: ${failed_inscripto?.reason}`);
        }
      }
    }
  } catch (error) {
    console.error("Error:", error);
  } finally {
    // Destroy the connection pool
    await knex_conn.destroy();
  }
}

if (import.meta.url.split("/").at(-1) === process.argv[1].split("/").at(-1)) {
  await sync_inscripciones();
  process.exit();
}
