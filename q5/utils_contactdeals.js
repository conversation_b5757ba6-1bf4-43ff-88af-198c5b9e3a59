import axios from "axios";
import knex from "knex";
import { config, usr, psw } from "./config.js";
const knex_conn = knex({
  client: "mssql",
  connection: config,
});
import { to_cr_time_string, to_date_string } from "./primitives.js";

export function extract_deals(contact) {
  let deals_ids = [];
  if (contact.deals == null) {
    Function.prototype();
  } else {
    for (const deal of contact.deals) {
      deals_ids.push(deal.id);
    }
  }
  return deals_ids;
}

export function fix_deal(deal) {
  let fixed_deal = deal;

  if (deal.created != null) {
    fixed_deal.created = to_cr_time_string(deal.created);
  }
  if (deal.modified != null) {
    fixed_deal.modified = to_cr_time_string(deal.modified);
  }
  if (deal.last_contact != null) {
    fixed_deal.last_contact = to_cr_time_string(deal.last_contact);
  }
  // if (deal.expected_closed_date != null) {
  //   fixed_deal.expected_closed_date = to_cr_time_string(
  //     deal.expected_closed_date
  //   );
  // }
  // if (deal.actual_closed_date != null) {
  //   fixed_deal.actual_closed_date = to_date_string(deal.actual_closed_date);
  // }

  return fixed_deal;
}

export async function add_deal(deal, contacto_id) {
  await knex_conn("Deals")
    .select()
    .where("Deal_id", deal.id)
    .then(async (rows) => {
      if (rows.length === 0) {
        console.log(deal.id);
        await knex_conn("Deals").insert({
          Deal_id: deal.id,
          Contacto_id: contacto_id,
          Deal_url: deal.url,
          Propietario: deal.owner_name,
          Propietario_mail: deal.owner,
          Nombre: deal.contact_name,
          Email: deal.contact_email,
          Telefono: deal.contact_phone,
          Origen: deal.contact_source,
          Medio: deal.contact_medium,
          Estado: deal.status,
          Estado_descuento: deal.status_desc,
          Creado: deal.created,
          Modificado: deal.modified,
          Expiracion_esperada: deal.expected_closed_date,
          Expiracion_final: deal.actual_closed_date,
        });
      } else {
        await knex_conn("Deals")
          .update({
            Contacto_id: contacto_id,
            Deal_url: deal.url,
            Propietario: deal.owner_name,
            Propietario_mail: deal.owner,
            Nombre: deal.contact_name,
            Email: deal.contact_email,
            Telefono: deal.contact_phone,
            Origen: deal.contact_source,
            Medio: deal.contact_medium,
            Estado: deal.status,
            Estado_descuento: deal.status_desc,
            Creado: deal.created,
            Modificado: deal.modified,
            Expiracion_esperada: deal.expected_closed_date,
            Expiracion_final: deal.actual_closed_date,
          })
          .where("Deal_id", deal.id);
      }
    });
}

export async function get_deal(api_key, deal_id) {
  const DEALS_API_URL = `https://api.clientify.net/v1/deals/${deal_id}/?page_size=200`;
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  try {
    response = await axios.get(DEALS_API_URL, { headers: header });
  } catch (error) {
    console.log(error);
    throw error;
  }

  if (response.status == 200) {
    return response.data;
  } else {
    console.log(response);
    return "";
  }
}

export async function list_deals(api_key) {
  const DEALS_API_URL = `https://api.clientify.net/v1/deals/`;
  const header = {
    Authorization: `Token ${api_key}`,
  };

  let response = undefined;
  try {
    response = await axios.get(DEALS_API_URL, { headers: header });
  } catch (error) {
    console.log(error);
    throw error;
  }

  if (response.status == 200) {
    return response.data;
  } else {
    console.log(response);
    return "";
  }
}
